package com.yunqu.park.iot.model.dto.command;

import com.yunqu.park.iot.model.command.dto.SetUploadTimezoneParams;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 设置上传时区指令请求
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "设置上传时区指令请求")
public class SetUploadTimezoneRequest extends BaseCommandReq {

    @Schema(description = "上传时区参数")
    private SetUploadTimezoneParams params;
}