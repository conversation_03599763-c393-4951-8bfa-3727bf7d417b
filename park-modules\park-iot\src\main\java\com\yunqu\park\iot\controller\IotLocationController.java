package com.yunqu.park.iot.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.yunqu.park.common.core.domain.R;
import com.yunqu.park.common.core.validate.AddGroup;
import com.yunqu.park.common.core.validate.EditGroup;
import com.yunqu.park.common.excel.utils.ExcelUtil;
import com.yunqu.park.common.idempotent.annotation.RepeatSubmit;
import com.yunqu.park.common.log.annotation.Log;
import com.yunqu.park.common.log.enums.BusinessType;
import com.yunqu.park.common.mybatis.core.page.PageQuery;
import com.yunqu.park.common.mybatis.core.page.TableDataInfo;
import com.yunqu.park.common.web.core.BaseController;
import com.yunqu.park.iot.domain.bo.IotLocationDataBo;
import com.yunqu.park.iot.domain.vo.IotLocationDataVo;
import com.yunqu.park.iot.service.IIotLocationDataService;
import lombok.RequiredArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import java.util.Date;
import java.util.List;

/**
 * 设备定位数据管理
 *
 * <AUTHOR>
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/iot/location")
public class IotLocationController extends BaseController {

    private final IIotLocationDataService iotLocationDataService;

    /**
     * 查询设备定位数据列表
     */
    @SaCheckPermission("iot:location:list")
    @GetMapping("/list")
    public TableDataInfo<IotLocationDataVo> list(IotLocationDataBo bo, PageQuery pageQuery) {
        return iotLocationDataService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出设备定位数据列表
     */
    @SaCheckPermission("iot:location:export")
    @Log(title = "设备定位数据", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(IotLocationDataBo bo, HttpServletResponse response) {
        List<IotLocationDataVo> list = iotLocationDataService.queryList(bo);
        ExcelUtil.exportExcel(list, "设备定位数据", IotLocationDataVo.class, response);
    }

    /**
     * 获取设备定位数据详细信息
     */
    @SaCheckPermission("iot:location:query")
    @GetMapping("/{locationId}")
    public R<IotLocationDataVo> getInfo(@NotNull(message = "主键不能为空")
                                        @PathVariable Long locationId) {
        return R.ok(iotLocationDataService.queryById(locationId));
    }

    /**
     * 新增设备定位数据
     */
    @SaCheckPermission("iot:location:add")
    @Log(title = "设备定位数据", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody IotLocationDataBo bo) {
        return toAjax(iotLocationDataService.insertByBo(bo));
    }

    /**
     * 修改设备定位数据
     */
    @SaCheckPermission("iot:location:edit")
    @Log(title = "设备定位数据", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody IotLocationDataBo bo) {
        return toAjax(iotLocationDataService.updateByBo(bo));
    }

    /**
     * 删除设备定位数据
     */
    @SaCheckPermission("iot:location:remove")
    @Log(title = "设备定位数据", businessType = BusinessType.DELETE)
    @DeleteMapping("/{locationIds}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] locationIds) {
        return toAjax(iotLocationDataService.deleteWithValidByIds(List.of(locationIds), true));
    }

    /**
     * 获取设备当前位置
     */
    @SaCheckPermission("iot:location:query")
    @GetMapping("/current/{imei}")
    public R<IotLocationDataVo> getCurrentLocation(@NotBlank(message = "IMEI不能为空")
                                                   @PathVariable String imei) {
        return R.ok(iotLocationDataService.getCurrentLocation(imei));
    }

    /**
     * 查询设备历史轨迹
     */
    @SaCheckPermission("iot:location:query")
    @GetMapping("/track/{imei}")
    public R<List<IotLocationDataVo>> getTrack(
            @NotBlank(message = "IMEI不能为空") @PathVariable String imei,
            @NotNull(message = "开始时间不能为空") @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date startTime,
            @NotNull(message = "结束时间不能为空") @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date endTime) {
        return R.ok(iotLocationDataService.getTrackData(imei, startTime, endTime));
    }

    /**
     * 获取轨迹回放数据
     */
    @SaCheckPermission("iot:location:query")
    @GetMapping("/playback/{imei}")
    public R<List<IotLocationDataVo>> getPlaybackData(
            @NotBlank(message = "IMEI不能为空") @PathVariable String imei,
            @NotNull(message = "开始时间不能为空") @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date startTime,
            @NotNull(message = "结束时间不能为空") @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date endTime,
            @RequestParam(defaultValue = "60") Integer interval) {
        return R.ok(iotLocationDataService.getPlaybackData(imei, startTime, endTime, interval));
    }
}
