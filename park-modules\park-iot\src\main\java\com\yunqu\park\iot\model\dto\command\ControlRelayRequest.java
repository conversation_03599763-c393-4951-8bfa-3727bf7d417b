package com.yunqu.park.iot.model.dto.command;

import com.yunqu.park.iot.model.command.dto.ControlRelayParams;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 控制继电器指令请求
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "控制继电器指令请求")
public class ControlRelayRequest extends BaseCommandReq {

    @Schema(description = "继电器控制参数")
    private ControlRelayParams params;
}