package com.yunqu.park.iot.service.command.impl;

import com.yunqu.park.iot.model.command.CommandType;
import com.yunqu.park.iot.model.command.dto.SetGpsSleepWorkParams;
import com.yunqu.park.iot.service.command.ICommand;
import lombok.RequiredArgsConstructor;

@RequiredArgsConstructor
public class SetGpsSleepWorkCommand implements ICommand {

    private final SetGpsSleepWorkParams params;
    private static final String TEMPLATE = "GPSON,%d#";

    @Override
    public CommandType getType() {
        return CommandType.SET_GPS_SLEEP_WORK;
    }

    @Override
    public String build() {
        return String.format(TEMPLATE, params.getState());
    }

    @Override
    public String getDescription() {
        return String.format("%s: %s", getType().getDescription(), params.getState() == 1 ? "开启" : "关闭");
    }
}