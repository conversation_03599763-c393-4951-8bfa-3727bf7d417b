package com.yunqu.park.iot.model.dto.command;

import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.Max;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 设置碰撞报警请求
 * 
 * <AUTHOR>
 * @since 2025-01-06
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class SetCollisionAlarmRequest extends BaseCommandReq {
    
    /**
     * 是否启用碰撞报警
     * true: 启用, false: 禁用
     */
    @NotNull(message = "碰撞报警开关不能为空")
    private Boolean enabled;
    
    /**
     * 碰撞报警灵敏度 (1-10, 1最敏感)
     */
    @Min(value = 1, message = "碰撞报警灵敏度不能小于1")
    @Max(value = 10, message = "碰撞报警灵敏度不能大于10")
    private Integer sensitivity;
}