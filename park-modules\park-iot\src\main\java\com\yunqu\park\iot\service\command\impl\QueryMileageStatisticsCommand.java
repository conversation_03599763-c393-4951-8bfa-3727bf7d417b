package com.yunqu.park.iot.service.command.impl;

import com.yunqu.park.iot.model.command.CommandType;
import com.yunqu.park.iot.service.command.ICommand;
import lombok.RequiredArgsConstructor;

@RequiredArgsConstructor
public class QueryMileageStatisticsCommand implements ICommand {

    private static final String TEMPLATE = "MILEAGE#";

    @Override
    public CommandType getType() {
        return CommandType.QUERY_MILEAGE_STATISTICS;
    }

    @Override
    public String build() {
        return TEMPLATE;
    }

    @Override
    public String getDescription() {
        return getType().getDescription();
    }
}