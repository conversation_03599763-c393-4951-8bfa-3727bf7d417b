package com.yunqu.park.iot.model.dto.command;

import com.yunqu.park.iot.model.command.dto.SetSosNumberParams;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 设置SOS号码指令请求
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "设置SOS号码指令请求")
public class SetSosNumberRequest extends BaseCommandReq {

    @Schema(description = "SOS号码参数")
    private SetSosNumberParams params;
}