package com.yunqu.park.iot.service.impl;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yunqu.park.common.core.exception.ServiceException;
import com.yunqu.park.common.core.utils.MapstructUtils;
import com.yunqu.park.common.mybatis.core.page.PageQuery;
import com.yunqu.park.common.mybatis.core.page.TableDataInfo;
import com.yunqu.park.common.redis.utils.RedisUtils;
import com.yunqu.park.iot.constant.IotConstants;
import com.yunqu.park.iot.domain.IotDevice;
import com.yunqu.park.iot.domain.bo.IotDeviceBo;
import com.yunqu.park.iot.domain.vo.IotDeviceVo;
import com.yunqu.park.iot.mapper.IotDeviceMapper;
import com.yunqu.park.iot.service.IIotDeviceService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.Duration;
import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * IoT设备Service业务层处理
 *
 * <AUTHOR>
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class IotDeviceServiceImpl extends ServiceImpl<IotDeviceMapper, IotDevice> implements IIotDeviceService {

    private final IotDeviceMapper baseMapper;

    /**
     * 查询IoT设备
     */
    @Override
    public IotDeviceVo queryById(Long deviceId) {
        return baseMapper.selectVoById(deviceId);
    }

    /**
     * 查询IoT设备列表
     */
    @Override
    public TableDataInfo<IotDeviceVo> queryPageList(IotDeviceBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<IotDevice> lqw = buildQueryWrapper(bo);
        Page<IotDeviceVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询IoT设备列表
     */
    @Override
    public List<IotDeviceVo> queryList(IotDeviceBo bo) {
        LambdaQueryWrapper<IotDevice> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<IotDevice> buildQueryWrapper(IotDeviceBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<IotDevice> lqw = Wrappers.lambdaQuery(IotDevice.class);
        lqw.eq(StrUtil.isNotBlank(bo.getImei()), IotDevice::getImei, bo.getImei());
        lqw.like(StrUtil.isNotBlank(bo.getDeviceName()), IotDevice::getDeviceName, bo.getDeviceName());
        lqw.eq(StrUtil.isNotBlank(bo.getDeviceType()), IotDevice::getDeviceType, bo.getDeviceType());
        lqw.eq(StrUtil.isNotBlank(bo.getStatus()), IotDevice::getStatus, bo.getStatus());
        lqw.between(params.get("beginRegisterTime") != null && params.get("endRegisterTime") != null,
            IotDevice::getRegisterTime, params.get("beginRegisterTime"), params.get("endRegisterTime"));
        return lqw;
    }

    /**
     * 新增IoT设备
     */
    @Override
    public Boolean insertByBo(IotDeviceBo bo) {
        IotDevice add = MapstructUtils.convert(bo, IotDevice.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setDeviceId(add.getDeviceId());
        }
        return flag;
    }

    /**
     * 修改IoT设备
     */
    @Override
    public Boolean updateByBo(IotDeviceBo bo) {
        IotDevice update = MapstructUtils.convert(bo, IotDevice.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(IotDevice entity) {
        // 校验IMEI唯一性
        if (StrUtil.isNotEmpty(entity.getImei())) {
            LambdaQueryWrapper<IotDevice> lqw = Wrappers.lambdaQuery();
            lqw.eq(IotDevice::getImei, entity.getImei());
            lqw.ne(entity.getDeviceId() != null, IotDevice::getDeviceId, entity.getDeviceId());
            long count = baseMapper.selectCount(lqw);
            if (count > 0) {
                throw new ServiceException("IMEI号已存在");
            }
        }
    }

    /**
     * 批量删除IoT设备
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            // 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteBatchIds(ids) > 0;
    }

    @Override
    public IotDeviceVo queryByImei(String imei) {
        return baseMapper.selectByImei(imei);
    }

    @Override
    public IotDevice getDeviceByImei(String imei) {
        return baseMapper.selectDeviceByImei(imei);
    }

    @Override
    public void handleDeviceOnline(String imei, String clientIp) {
        try {
            log.info("[DEVICE-ONLINE] Processing device online: IMEI={}, ClientIP={}", imei, clientIp);

            // 更新设备在线状态
            baseMapper.updateDeviceStatus(imei, IotConstants.DeviceStatus.ONLINE, clientIp);
            log.debug("[DEVICE-ONLINE] Database status updated: IMEI={}, Status=ONLINE", imei);

            // 缓存设备在线状态
            String cacheKey = IotConstants.CacheKeys.DEVICE_STATUS_PREFIX + imei;
            RedisUtils.setCacheObject(cacheKey, IotConstants.DeviceStatus.ONLINE, Duration.ofMinutes(10));
            log.debug("[DEVICE-ONLINE] Cache status updated: IMEI={}, CacheKey={}", imei, cacheKey);

            // 添加到在线设备集合
            RedisUtils.addCacheSet(IotConstants.CacheKeys.ONLINE_DEVICES, imei);
            log.debug("[DEVICE-ONLINE] Added to online devices set: IMEI={}", imei);

            log.info("[DEVICE-ONLINE] ✅ Device successfully online: IMEI={}, ClientIP={}", imei, clientIp);
        } catch (Exception e) {
            log.error("[DEVICE-ONLINE] ❌ Failed to handle device online: IMEI={}, ClientIP={}, Error={}",
                     imei, clientIp, e.getMessage(), e);
        }
    }

    @Override
    public void handleDeviceOffline(String imei) {
        try {
            log.info("[DEVICE-OFFLINE] Processing device offline: IMEI={}", imei);

            // 更新设备离线状态
            baseMapper.updateDeviceStatus(imei, IotConstants.DeviceStatus.OFFLINE, null);
            log.debug("[DEVICE-OFFLINE] Database status updated: IMEI={}, Status=OFFLINE", imei);

            // 删除缓存
            String cacheKey = IotConstants.CacheKeys.DEVICE_STATUS_PREFIX + imei;
            RedisUtils.deleteObject(cacheKey);
            log.debug("[DEVICE-OFFLINE] Cache status cleared: IMEI={}, CacheKey={}", imei, cacheKey);

            // 从在线设备集合中移除
            RedisUtils.removeCacheSet(IotConstants.CacheKeys.ONLINE_DEVICES, imei);
            log.debug("[DEVICE-OFFLINE] Removed from online devices set: IMEI={}", imei);

            log.info("[DEVICE-OFFLINE] ✅ Device successfully offline: IMEI={}", imei);
        } catch (Exception e) {
            log.error("[DEVICE-OFFLINE] ❌ Failed to handle device offline: IMEI={}, Error={}",
                     imei, e.getMessage(), e);
        }
    }

    @Override
    public void updateDeviceSimInfo(String imei, String imsi, String iccid) {
        try {
            baseMapper.updateDeviceSimInfo(imei, imsi, iccid);
            log.debug("Updated SIM info for device {}: IMSI={}, ICCID={}", imei, imsi, iccid);
        } catch (Exception e) {
            log.error("Failed to update device SIM info: {}", e.getMessage(), e);
        }
    }

    @Override
    public boolean existsByImei(String imei) {
        LambdaQueryWrapper<IotDevice> lqw = Wrappers.lambdaQuery();
        lqw.eq(IotDevice::getImei, imei);
        return baseMapper.selectCount(lqw) > 0;
    }

    @Override
    public Object getDeviceStatistics() {
        return baseMapper.selectDeviceStatistics();
    }
}
