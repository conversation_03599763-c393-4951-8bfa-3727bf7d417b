package com.yunqu.park.iot.test;

import com.yunqu.park.common.core.domain.R;
import com.yunqu.park.iot.utils.ByteBufUtils;
import io.netty.buffer.ByteBuf;
import io.netty.buffer.Unpooled;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;

/**
 * 协议解码测试控制器
 * 用于测试GT06协议解码功能和调试解码问题
 *
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/iot/test/protocol")
public class ProtocolDecodeTestController {

    /**
     * 测试字节缓冲区安全操作
     */
    @PostMapping("/test-bytebuf-safety")
    public R<Map<String, Object>> testByteBufSafety(@RequestBody TestByteBufRequest request) {
        Map<String, Object> result = new HashMap<>();

        try {
            // 创建测试数据
            byte[] testData = hexStringToBytes(request.getHexData());
            ByteBuf buf = Unpooled.wrappedBuffer(testData);

            result.put("originalData", request.getHexData());
            result.put("dataLength", testData.length);
            result.put("readableBytes", buf.readableBytes());

            // 测试安全读取操作
            Map<String, Object> safetyTests = new HashMap<>();

            // 测试读取2字节（起始位）
            byte[] startFlag = ByteBufUtils.safeReadBytes(buf, 2);
            safetyTests.put("startFlag", startFlag != null ? ByteBufUtils.bytesToHexString(startFlag) : "null");

            // 测试读取1字节（包长度）
            Byte packetLength = ByteBufUtils.safeReadByte(buf);
            safetyTests.put("packetLength", packetLength != null ? String.format("0x%02X (%d)", packetLength, packetLength & 0xFF) : "null");

            // 测试读取剩余数据
            int remaining = buf.readableBytes();
            safetyTests.put("remainingBytes", remaining);

            if (remaining > 0) {
                byte[] remainingData = ByteBufUtils.safeReadBytes(buf, remaining);
                safetyTests.put("remainingData", remainingData != null ? ByteBufUtils.bytesToHexString(remainingData) : "null");
            }

            result.put("safetyTests", safetyTests);
            result.put("success", true);

        } catch (Exception e) {
            log.error("ByteBuf safety test failed: {}", e.getMessage(), e);
            result.put("error", e.getMessage());
            result.put("success", false);
        }

        return R.ok(result);
    }

    /**
     * 测试边界异常情况
     */
    @PostMapping("/test-boundary-exception")
    public R<Map<String, Object>> testBoundaryException(@RequestBody TestByteBufRequest request) {
        Map<String, Object> result = new HashMap<>();

        try {
            // 模拟导致越界的数据包
            String testHex = request.getHexData();
            if (testHex == null || testHex.isEmpty()) {
                // 使用导致越界的测试数据：readerIndex(3) + length(16) exceeds writerIndex(18)
                testHex = "787812345678901234567890"; // 18字节数据
            }

            byte[] testData = hexStringToBytes(testHex);
            ByteBuf buf = Unpooled.wrappedBuffer(testData);

            result.put("originalHex", testHex);
            result.put("totalBytes", testData.length);
            result.put("initialReadableBytes", buf.readableBytes());

            Map<String, Object> boundaryTests = new HashMap<>();

            // 模拟解码器的读取过程
            // 1. 读取起始位（2字节）
            byte[] startFlag = ByteBufUtils.safeReadBytes(buf, 2);
            boundaryTests.put("step1_startFlag", startFlag != null ? ByteBufUtils.bytesToHexString(startFlag) : "FAILED");
            boundaryTests.put("step1_remainingBytes", buf.readableBytes());

            // 2. 读取包长度（1字节）
            Byte packetLength = ByteBufUtils.safeReadByte(buf);
            boundaryTests.put("step2_packetLength", packetLength != null ? String.format("0x%02X (%d)", packetLength, packetLength & 0xFF) : "FAILED");
            boundaryTests.put("step2_remainingBytes", buf.readableBytes());

            if (packetLength != null) {
                int packetLengthValue = packetLength & 0xFF;

                // 3. 尝试读取数据包内容（这里可能越界）
                boundaryTests.put("step3_requestedLength", packetLengthValue);
                boundaryTests.put("step3_availableBytes", buf.readableBytes());

                byte[] packetData = ByteBufUtils.safeReadBytes(buf, packetLengthValue);
                boundaryTests.put("step3_packetData", packetData != null ?
                    String.format("SUCCESS: %d bytes", packetData.length) : "FAILED - Insufficient data");

                if (packetData != null) {
                    boundaryTests.put("step4_finalRemainingBytes", buf.readableBytes());

                    // 4. 尝试读取停止位
                    byte[] stopFlag = ByteBufUtils.safeReadBytes(buf, 2);
                    boundaryTests.put("step4_stopFlag", stopFlag != null ? ByteBufUtils.bytesToHexString(stopFlag) : "FAILED");
                }
            }

            result.put("boundaryTests", boundaryTests);
            result.put("success", true);

        } catch (Exception e) {
            log.error("Boundary exception test failed: {}", e.getMessage(), e);
            result.put("error", e.getMessage());
            result.put("success", false);
        }

        return R.ok(result);
    }

    /**
     * 分析GT06协议数据包结构
     */
    @PostMapping("/analyze-packet")
    public R<Map<String, Object>> analyzePacket(@RequestBody TestByteBufRequest request) {
        Map<String, Object> result = new HashMap<>();
        
        try {
            byte[] data = hexStringToBytes(request.getHexData());
            result.put("originalHex", request.getHexData());
            result.put("totalLength", data.length);
            
            if (data.length < 6) {
                result.put("error", "Data too short for GT06 packet (minimum 6 bytes)");
                return R.ok(result);
            }
            
            Map<String, Object> analysis = new HashMap<>();
            
            // 分析起始位
            byte[] startFlag = {data[0], data[1]};
            analysis.put("startFlag", ByteBufUtils.bytesToHexString(startFlag));
            analysis.put("startFlagValid", isValidStartFlag(startFlag));
            
            // 分析包长度
            int packetLength = data[2] & 0xFF;
            analysis.put("packetLength", String.format("0x%02X (%d)", data[2], packetLength));
            
            // 计算期望的总长度
            int expectedTotalLength = 2 + 1 + packetLength + 2; // 起始位 + 包长度 + 包内容 + 停止位
            analysis.put("expectedTotalLength", expectedTotalLength);
            analysis.put("actualTotalLength", data.length);
            analysis.put("lengthMatch", expectedTotalLength == data.length);
            
            // 分析协议号
            if (data.length > 3) {
                analysis.put("protocolNumber", String.format("0x%02X", data[3]));
            }
            
            // 分析停止位
            if (data.length >= 2) {
                byte[] stopFlag = {data[data.length - 2], data[data.length - 1]};
                analysis.put("stopFlag", ByteBufUtils.bytesToHexString(stopFlag));
                analysis.put("stopFlagValid", isValidStopFlag(stopFlag));
            }
            
            // 数据完整性检查
            analysis.put("dataIntegrity", analyzeDataIntegrity(data, packetLength));
            
            result.put("analysis", analysis);
            result.put("success", true);
            
        } catch (Exception e) {
            log.error("Packet analysis failed: {}", e.getMessage(), e);
            result.put("error", e.getMessage());
            result.put("success", false);
        }
        
        return R.ok(result);
    }

    /**
     * 模拟解码过程
     */
    @PostMapping("/simulate-decode")
    public R<Map<String, Object>> simulateDecode(@RequestBody TestByteBufRequest request) {
        Map<String, Object> result = new HashMap<>();
        
        try {
            byte[] data = hexStringToBytes(request.getHexData());
            ByteBuf buf = Unpooled.wrappedBuffer(data);
            
            result.put("originalHex", request.getHexData());
            result.put("totalBytes", data.length);
            
            Map<String, Object> decodeSteps = new HashMap<>();
            
            // 步骤1：检查最小长度
            decodeSteps.put("step1_minLength", buf.readableBytes() >= 6);
            
            // 步骤2：读取起始位
            buf.markReaderIndex();
            byte[] startFlag = ByteBufUtils.safeReadBytes(buf, 2);
            decodeSteps.put("step2_startFlag", startFlag != null ? ByteBufUtils.bytesToHexString(startFlag) : "FAILED");
            
            if (startFlag == null) {
                decodeSteps.put("result", "FAILED - Cannot read start flag");
                result.put("decodeSteps", decodeSteps);
                return R.ok(result);
            }
            
            // 步骤3：读取包长度
            Byte packetLengthByte = ByteBufUtils.safeReadByte(buf);
            decodeSteps.put("step3_packetLength", packetLengthByte != null ? String.format("0x%02X (%d)", packetLengthByte, packetLengthByte & 0xFF) : "FAILED");
            
            if (packetLengthByte == null) {
                decodeSteps.put("result", "FAILED - Cannot read packet length");
                result.put("decodeSteps", decodeSteps);
                return R.ok(result);
            }
            
            int packetLength = packetLengthByte & 0xFF;
            
            // 步骤4：检查数据完整性
            int remainingNeeded = packetLength + 2; // 包内容 + 停止位
            boolean hasEnoughData = buf.readableBytes() >= remainingNeeded;
            decodeSteps.put("step4_dataIntegrity", String.format("Need %d bytes, have %d bytes, sufficient: %s", 
                    remainingNeeded, buf.readableBytes(), hasEnoughData));
            
            if (!hasEnoughData) {
                decodeSteps.put("result", "FAILED - Insufficient data");
                result.put("decodeSteps", decodeSteps);
                return R.ok(result);
            }
            
            // 步骤5：读取包内容
            byte[] packetData = ByteBufUtils.safeReadBytes(buf, packetLength);
            decodeSteps.put("step5_packetData", packetData != null ? 
                    String.format("Read %d bytes: %s", packetLength, ByteBufUtils.bytesToHexString(packetData)) : "FAILED");
            
            // 步骤6：读取停止位
            byte[] stopFlag = ByteBufUtils.safeReadBytes(buf, 2);
            decodeSteps.put("step6_stopFlag", stopFlag != null ? ByteBufUtils.bytesToHexString(stopFlag) : "FAILED");
            
            if (packetData != null && stopFlag != null) {
                decodeSteps.put("result", "SUCCESS - Packet decoded successfully");
            } else {
                decodeSteps.put("result", "FAILED - Cannot read packet data or stop flag");
            }
            
            result.put("decodeSteps", decodeSteps);
            result.put("success", true);
            
        } catch (Exception e) {
            log.error("Decode simulation failed: {}", e.getMessage(), e);
            result.put("error", e.getMessage());
            result.put("success", false);
        }
        
        return R.ok(result);
    }

    /**
     * 检查起始位是否有效
     */
    private boolean isValidStartFlag(byte[] startFlag) {
        if (startFlag == null || startFlag.length != 2) {
            return false;
        }
        return (startFlag[0] == 0x78 && startFlag[1] == 0x78) || 
               (startFlag[0] == 0x79 && startFlag[1] == 0x79);
    }

    /**
     * 检查停止位是否有效
     */
    private boolean isValidStopFlag(byte[] stopFlag) {
        if (stopFlag == null || stopFlag.length != 2) {
            return false;
        }
        return stopFlag[0] == 0x0D && stopFlag[1] == 0x0A;
    }

    /**
     * 分析数据完整性
     */
    private Map<String, Object> analyzeDataIntegrity(byte[] data, int packetLength) {
        Map<String, Object> integrity = new HashMap<>();
        
        int expectedLength = 2 + 1 + packetLength + 2; // 起始位 + 包长度 + 包内容 + 停止位
        integrity.put("expectedLength", expectedLength);
        integrity.put("actualLength", data.length);
        integrity.put("lengthMatch", expectedLength == data.length);
        
        if (data.length < expectedLength) {
            integrity.put("status", "INCOMPLETE");
            integrity.put("missingBytes", expectedLength - data.length);
        } else if (data.length > expectedLength) {
            integrity.put("status", "EXTRA_DATA");
            integrity.put("extraBytes", data.length - expectedLength);
        } else {
            integrity.put("status", "COMPLETE");
        }
        
        return integrity;
    }

    /**
     * 将十六进制字符串转换为字节数组
     */
    private byte[] hexStringToBytes(String hexString) {
        if (hexString == null || hexString.isEmpty()) {
            return new byte[0];
        }
        
        // 移除空格和其他分隔符
        hexString = hexString.replaceAll("[\\s-:]", "");
        
        if (hexString.length() % 2 != 0) {
            throw new IllegalArgumentException("Hex string length must be even");
        }
        
        byte[] bytes = new byte[hexString.length() / 2];
        for (int i = 0; i < bytes.length; i++) {
            bytes[i] = (byte) Integer.parseInt(hexString.substring(i * 2, i * 2 + 2), 16);
        }
        
        return bytes;
    }

    /**
     * 测试请求对象
     */
    public static class TestByteBufRequest {
        private String hexData;
        
        public String getHexData() { return hexData; }
        public void setHexData(String hexData) { this.hexData = hexData; }
    }
}
