package com.yunqu.park.iot.netty.handler;

import io.netty.buffer.ByteBuf;
import io.netty.buffer.ByteBufHolder;
import io.netty.buffer.ByteBufUtil;
import io.netty.channel.ChannelDuplexHandler;
import io.netty.channel.ChannelHandler;
import io.netty.channel.ChannelHandlerContext;
import io.netty.channel.ChannelPromise;
import io.netty.handler.logging.ByteBufFormat;
import io.netty.util.internal.ObjectUtil;
import io.netty.util.internal.StringUtil;
import lombok.extern.slf4j.Slf4j;

import java.net.SocketAddress;

@Slf4j
@ChannelHandler.Sharable
public class LoggingHandler extends ChannelDuplexHandler {


    private final ByteBufFormat byteBufFormat;

    public LoggingHandler() {
        this.byteBufFormat = (ByteBufFormat) ObjectUtil.checkNotNull(ByteBufFormat.HEX_DUMP, "byteBufFormat");
    }

    public void channelRegistered(ChannelHandlerContext ctx) throws Exception {
        log.info(format(ctx, "REGISTERED"));
        ctx.fireChannelRegistered();
    }

    public void channelUnregistered(ChannelHandlerContext ctx) throws Exception {
        log.info(format(ctx, "UNREGISTERED"));
        ctx.fireChannelUnregistered();
    }

    public void channelActive(ChannelHandlerContext ctx) throws Exception {
        log.info(format(ctx, "ACTIVE"));
        ctx.fireChannelActive();
    }

    public void channelInactive(ChannelHandlerContext ctx) throws Exception {
        log.info(format(ctx, "INACTIVE"));
        ctx.fireChannelInactive();
    }

    public void exceptionCaught(ChannelHandlerContext ctx, Throwable cause) throws Exception {
        log.info(format(ctx, "EXCEPTION", cause), cause);
        ctx.fireExceptionCaught(cause);
    }

    public void userEventTriggered(ChannelHandlerContext ctx, Object evt) throws Exception {
        log.info(format(ctx, "USER_EVENT", evt));
        ctx.fireUserEventTriggered(evt);
    }

    public void bind(ChannelHandlerContext ctx, SocketAddress localAddress, ChannelPromise promise) throws Exception {
        log.info(format(ctx, "BIND", localAddress));
        ctx.bind(localAddress, promise);
    }

    public void connect(ChannelHandlerContext ctx, SocketAddress remoteAddress, SocketAddress localAddress,
                        ChannelPromise promise) throws Exception {
        log.info(format(ctx, "CONNECT", remoteAddress, localAddress));
        ctx.connect(remoteAddress, localAddress, promise);
    }

    public void disconnect(ChannelHandlerContext ctx, ChannelPromise promise) throws Exception {
        log.info(format(ctx, "DISCONNECT"));
        ctx.disconnect(promise);
    }

    public void close(ChannelHandlerContext ctx, ChannelPromise promise) throws Exception {
        log.info(format(ctx, "CLOSE"));
        ctx.close(promise);
    }

    public void deregister(ChannelHandlerContext ctx, ChannelPromise promise) throws Exception {
        log.info(format(ctx, "DEREGISTER"));
        ctx.deregister(promise);
    }

    public void channelReadComplete(ChannelHandlerContext ctx) throws Exception {
        log.info(format(ctx, "READ COMPLETE"));
        ctx.fireChannelReadComplete();
    }

    public void channelRead(ChannelHandlerContext ctx, Object msg) throws Exception {
        log.info(format(ctx, "READ", msg));
        ctx.fireChannelRead(msg);
    }

    public void write(ChannelHandlerContext ctx, Object msg, ChannelPromise promise) throws Exception {
        log.info(format(ctx, "WRITE", msg));
        ctx.write(msg, promise);
    }

    public void channelWritabilityChanged(ChannelHandlerContext ctx) throws Exception {
        log.info(format(ctx, "WRITABILITY CHANGED"));
        ctx.fireChannelWritabilityChanged();
    }

    public void flush(ChannelHandlerContext ctx) throws Exception {
        log.info(format(ctx, "FLUSH"));
        ctx.flush();
    }

    protected String format(ChannelHandlerContext ctx, String eventName) {
        String chStr = ctx.channel().toString();
        return (new StringBuilder(chStr.length() + 1 + eventName.length())).append(chStr).append(' ').append(eventName)
            .toString();
    }

    protected String format(ChannelHandlerContext ctx, String eventName, Object arg) {
        if (arg instanceof ByteBuf)
            return formatByteBuf(ctx, eventName, (ByteBuf) arg);
        if (arg instanceof ByteBufHolder)
            return formatByteBufHolder(ctx, eventName, (ByteBufHolder) arg);
        return formatSimple(ctx, eventName, arg);
    }

    protected String format(ChannelHandlerContext ctx, String eventName, Object firstArg, Object secondArg) {
        if (secondArg == null)
            return formatSimple(ctx, eventName, firstArg);
        String chStr = ctx.channel().toString();
        String arg1Str = String.valueOf(firstArg);
        String arg2Str = secondArg.toString();
        StringBuilder buf = new StringBuilder(
            chStr.length() + 1 + eventName.length() + 2 + arg1Str.length() + 2 + arg2Str.length());
        buf.append(chStr).append(' ').append(eventName).append(": ").append(arg1Str).append(", ").append(arg2Str);
        return buf.toString();
    }

    private String formatByteBuf(ChannelHandlerContext ctx, String eventName, ByteBuf msg) {
        String chStr = ctx.channel().toString();
        int length = msg.readableBytes();
        if (length == 0) {
            StringBuilder stringBuilder = new StringBuilder(chStr.length() + 1 + eventName.length() + 4);
            stringBuilder.append(chStr).append(' ').append(eventName).append(": 0B");
            return stringBuilder.toString();
        }
        int outputLength = chStr.length() + 1 + eventName.length() + 2 + 10 + 1;
        if (this.byteBufFormat == ByteBufFormat.HEX_DUMP) {
            int rows = length / 16 + ((length % 15 == 0) ? 0 : 1) + 4;
            int hexDumpLength = 2 + rows * 80;
            outputLength += hexDumpLength;
        }
        StringBuilder buf = new StringBuilder(outputLength);
        buf.append(chStr).append(' ').append(eventName).append(": ").append(length).append('B');
        if (this.byteBufFormat == ByteBufFormat.HEX_DUMP) {
            buf.append(StringUtil.NEWLINE);
            ByteBufUtil.appendPrettyHexDump(buf, msg);
        }
        return buf.toString();
    }

    private String formatByteBufHolder(ChannelHandlerContext ctx, String eventName, ByteBufHolder msg) {
        String chStr = ctx.channel().toString();
        String msgStr = msg.toString();
        ByteBuf content = msg.content();
        int length = content.readableBytes();
        if (length == 0) {
            StringBuilder stringBuilder = new StringBuilder(
                chStr.length() + 1 + eventName.length() + 2 + msgStr.length() + 4);
            stringBuilder.append(chStr).append(' ').append(eventName).append(", ").append(msgStr).append(", 0B");
            return stringBuilder.toString();
        }
        int outputLength = chStr.length() + 1 + eventName.length() + 2 + msgStr.length() + 2 + 10 + 1;
        if (this.byteBufFormat == ByteBufFormat.HEX_DUMP) {
            int rows = length / 16 + ((length % 15 == 0) ? 0 : 1) + 4;
            int hexDumpLength = 2 + rows * 80;
            outputLength += hexDumpLength;
        }
        StringBuilder buf = new StringBuilder(outputLength);
        buf.append(chStr).append(' ').append(eventName).append(": ").append(msgStr).append(", ").append(length)
            .append('B');
        if (this.byteBufFormat == ByteBufFormat.HEX_DUMP) {
            buf.append(StringUtil.NEWLINE);
            ByteBufUtil.appendPrettyHexDump(buf, content);
        }
        return buf.toString();
    }

    private static String formatSimple(ChannelHandlerContext ctx, String eventName, Object msg) {
        String chStr = ctx.channel().toString();
        String msgStr = String.valueOf(msg);
        StringBuilder buf = new StringBuilder(chStr.length() + 1 + eventName.length() + 2 + msgStr.length());
        return buf.append(chStr).append(' ').append(eventName).append(": ").append(msgStr).toString();
    }
}
