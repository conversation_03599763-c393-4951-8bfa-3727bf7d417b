package com.yunqu.park.iot.utils;

import io.netty.buffer.ByteBuf;
import lombok.extern.slf4j.Slf4j;

/**
 * ByteBuf安全操作工具类
 * 提供安全的字节缓冲区读取操作，避免越界异常
 *
 * <AUTHOR>
 */
@Slf4j
public class ByteBufUtils {

    /**
     * 安全读取指定长度的字节数组
     * @param buf 字节缓冲区
     * @param length 要读取的长度
     * @return 读取的字节数组，如果数据不足则返回null
     */
    public static byte[] safeReadBytes(ByteBuf buf, int length) {
        if (buf == null || length <= 0) {
            log.debug("Invalid parameters: buf={}, length={}", buf != null ? "not null" : "null", length);
            return null;
        }

        // 增强边界检查
        if (length > 65536) { // 防止过大的读取请求
            log.warn("Read length too large: {}, max allowed: 65536", length);
            return null;
        }

        if (buf.readableBytes() < length) {
            log.debug("Insufficient data to read: required={}, available={}, readerIndex={}, writerIndex={}",
                     length, buf.readableBytes(), buf.readerIndex(), buf.writerIndex());
            return null;
        }

        try {
            byte[] data = new byte[length];
            buf.readBytes(data);
            return data;
        } catch (Exception e) {
            log.error("Exception during safe read: {}", e.getMessage(), e);
            return null;
        }
    }

    /**
     * 安全获取指定长度的字节数组（不移动读指针）
     * @param buf 字节缓冲区
     * @param index 起始索引
     * @param length 要获取的长度
     * @return 获取的字节数组，如果数据不足则返回null
     */
    public static byte[] safeGetBytes(ByteBuf buf, int index, int length) {
        if (buf == null || length <= 0 || index < 0) {
            return null;
        }
        
        if (index + length > buf.writerIndex()) {
            log.debug("Insufficient data to get: index={}, length={}, writerIndex={}", 
                     index, length, buf.writerIndex());
            return null;
        }
        
        byte[] data = new byte[length];
        buf.getBytes(index, data);
        return data;
    }

    /**
     * 安全读取单个字节
     * @param buf 字节缓冲区
     * @return 读取的字节，如果数据不足则返回null
     */
    public static Byte safeReadByte(ByteBuf buf) {
        if (buf == null || buf.readableBytes() < 1) {
            return null;
        }
        return buf.readByte();
    }

    /**
     * 安全读取短整型（2字节）
     * @param buf 字节缓冲区
     * @return 读取的短整型，如果数据不足则返回null
     */
    public static Short safeReadShort(ByteBuf buf) {
        if (buf == null || buf.readableBytes() < 2) {
            return null;
        }
        return buf.readShort();
    }

    /**
     * 安全读取整型（4字节）
     * @param buf 字节缓冲区
     * @return 读取的整型，如果数据不足则返回null
     */
    public static Integer safeReadInt(ByteBuf buf) {
        if (buf == null || buf.readableBytes() < 4) {
            return null;
        }
        return buf.readInt();
    }

    /**
     * 检查缓冲区是否有足够的数据
     * @param buf 字节缓冲区
     * @param requiredBytes 需要的字节数
     * @return 是否有足够的数据
     */
    public static boolean hasEnoughData(ByteBuf buf, int requiredBytes) {
        return buf != null && buf.readableBytes() >= requiredBytes;
    }

    /**
     * 将字节数组转换为十六进制字符串
     * @param bytes 字节数组
     * @return 十六进制字符串
     */
    public static String bytesToHexString(byte[] bytes) {
        if (bytes == null || bytes.length == 0) {
            return "";
        }
        StringBuilder sb = new StringBuilder();
        for (byte b : bytes) {
            sb.append(String.format("%02X ", b));
        }
        return sb.toString().trim();
    }

    /**
     * 将字节数组转换为带分隔符的十六进制字符串
     * @param bytes 字节数组
     * @param separator 分隔符
     * @return 带分隔符的十六进制字符串
     */
    public static String bytesToHexString(byte[] bytes, String separator) {
        if (bytes == null || bytes.length == 0) {
            return "";
        }
        if (separator == null) {
            separator = "";
        }
        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < bytes.length; i++) {
            sb.append(String.format("%02X", bytes[i]));
            if (i < bytes.length - 1) {
                sb.append(separator);
            }
        }
        return sb.toString();
    }

    /**
     * 将ByteBuf的内容转换为十六进制字符串（用于调试）
     * @param buf 字节缓冲区
     * @param maxLength 最大输出长度
     * @return 十六进制字符串
     */
    public static String bufToHexString(ByteBuf buf, int maxLength) {
        if (buf == null || buf.readableBytes() == 0) {
            return "";
        }

        try {
            int length = Math.min(buf.readableBytes(), Math.max(0, maxLength));
            if (length == 0) {
                return "";
            }

            // 增强边界检查
            if (buf.readerIndex() + length > buf.writerIndex()) {
                log.warn("Buffer bounds check failed: readerIndex={}, length={}, writerIndex={}",
                        buf.readerIndex(), length, buf.writerIndex());
                return "";
            }

            byte[] data = new byte[length];
            buf.getBytes(buf.readerIndex(), data);
            return bytesToHexString(data);
        } catch (Exception e) {
            log.error("Exception in bufToHexString: {}", e.getMessage());
            return "";
        }
    }

    /**
     * 安全跳过指定字节数
     * @param buf 字节缓冲区
     * @param bytes 要跳过的字节数
     * @return 实际跳过的字节数
     */
    public static int safeSkipBytes(ByteBuf buf, int bytes) {
        if (buf == null || bytes <= 0) {
            return 0;
        }
        
        int actualSkip = Math.min(bytes, buf.readableBytes());
        buf.skipBytes(actualSkip);
        return actualSkip;
    }

    /**
     * 查找字节模式在缓冲区中的位置
     * @param buf 字节缓冲区
     * @param pattern 要查找的字节模式
     * @return 找到的位置索引，未找到返回-1
     */
    public static int findPattern(ByteBuf buf, byte[] pattern) {
        if (buf == null || pattern == null || pattern.length == 0) {
            return -1;
        }
        
        int searchLength = buf.readableBytes() - pattern.length + 1;
        for (int i = 0; i < searchLength; i++) {
            boolean found = true;
            for (int j = 0; j < pattern.length; j++) {
                if (buf.getByte(buf.readerIndex() + i + j) != pattern[j]) {
                    found = false;
                    break;
                }
            }
            if (found) {
                return i;
            }
        }
        return -1;
    }

    /**
     * 验证缓冲区数据完整性
     * @param buf 字节缓冲区
     * @param expectedLength 期望的数据长度
     * @param description 数据描述（用于日志）
     * @return 是否完整
     */
    public static boolean validateDataIntegrity(ByteBuf buf, int expectedLength, String description) {
        if (buf == null) {
            log.warn("Buffer is null for {}", description);
            return false;
        }
        
        if (buf.readableBytes() < expectedLength) {
            log.warn("Insufficient data for {}: required={}, available={}", 
                    description, expectedLength, buf.readableBytes());
            return false;
        }
        
        return true;
    }
}
