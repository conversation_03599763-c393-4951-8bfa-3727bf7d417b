package com.yunqu.park.iot.model.dto.command;

import com.yunqu.park.iot.model.command.dto.SetAlarmParams;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 设置震动报警指令请求
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "设置震动报警指令请求")
public class SetVibrationAlarmRequest extends BaseCommandReq {

    @Schema(description = "震动报警参数")
    private SetAlarmParams params;
}