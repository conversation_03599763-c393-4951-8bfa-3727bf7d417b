package com.yunqu.park.iot.domain.bo;

import com.yunqu.park.common.core.validate.AddGroup;
import com.yunqu.park.common.core.validate.EditGroup;
import com.yunqu.park.common.mybatis.core.domain.BaseEntity;
import com.yunqu.park.iot.domain.IotLocationData;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.EqualsAndHashCode;

import jakarta.validation.constraints.*;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 设备定位数据业务对象 iot_location_data
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = IotLocationData.class, reverseConvertGenerate = false)
public class IotLocationDataBo extends BaseEntity {

    /**
     * 定位记录ID
     */
    @NotNull(message = "定位记录ID不能为空", groups = { EditGroup.class })
    private Long locationId;

    /**
     * 设备ID
     */
    @NotNull(message = "设备ID不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long deviceId;

    /**
     * 设备IMEI
     */
    @NotBlank(message = "设备IMEI不能为空", groups = { AddGroup.class, EditGroup.class })
    private String imei;

    /**
     * GPS时间
     */
    @NotNull(message = "GPS时间不能为空", groups = { AddGroup.class, EditGroup.class })
    private Date gpsTime;

    /**
     * 纬度
     */
    @DecimalMin(value = "-90.0", message = "纬度范围错误")
    @DecimalMax(value = "90.0", message = "纬度范围错误")
    private BigDecimal latitude;

    /**
     * 经度
     */
    @DecimalMin(value = "-180.0", message = "经度范围错误")
    @DecimalMax(value = "180.0", message = "经度范围错误")
    private BigDecimal longitude;

    /**
     * 海拔高度(米)
     */
    private Integer altitude;

    /**
     * 速度(km/h)
     */
    @Min(value = 0, message = "速度不能为负数")
    @Max(value = 255, message = "速度超出范围")
    private Integer speed;

    /**
     * 方向角(0-360度)
     */
    @Min(value = 0, message = "方向角不能为负数")
    @Max(value = 360, message = "方向角超出范围")
    private Integer direction;

    /**
     * 卫星数量
     */
    private Integer satelliteCount;

    /**
     * GPS状态:0-未定位,1-已定位
     */
    private String gpsStatus;

    /**
     * ACC状态:0-关闭,1-开启
     */
    private String accStatus;

    /**
     * 移动国家代码
     */
    private Integer mcc;

    /**
     * 移动网络代码
     */
    private Integer mnc;

    /**
     * 位置区码
     */
    private Integer lac;

    /**
     * 基站ID
     */
    private Integer cellId;

    /**
     * 信号强度(0-100)
     */
    @Min(value = 0, message = "信号强度不能为负数")
    @Max(value = 100, message = "信号强度超出范围")
    private Integer signalStrength;

    /**
     * 里程(米)
     */
    private Integer mileage;

}
