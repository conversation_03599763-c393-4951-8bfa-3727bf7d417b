package com.yunqu.park.iot.model.dto.command;

import com.yunqu.park.iot.model.command.dto.SetHeartbeatIntervalParams;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 设置心跳间隔指令请求
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "设置心跳间隔指令请求")
public class SetHeartbeatIntervalRequest extends BaseCommandReq {

    @Schema(description = "心跳间隔参数")
    private SetHeartbeatIntervalParams params;
}