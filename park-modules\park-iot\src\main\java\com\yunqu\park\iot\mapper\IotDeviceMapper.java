package com.yunqu.park.iot.mapper;

import com.yunqu.park.common.mybatis.core.mapper.BaseMapperPlus;
import com.yunqu.park.iot.domain.IotDevice;
import com.yunqu.park.iot.domain.vo.IotDeviceVo;
import org.apache.ibatis.annotations.Param;

/**
 * IoT设备Mapper接口
 *
 * <AUTHOR>
 */
public interface IotDeviceMapper extends BaseMapperPlus<IotDevice, IotDeviceVo> {

    /**
     * 根据IMEI查询设备信息
     * @param imei 设备IMEI号
     * @return 设备信息
     */
    IotDeviceVo selectByImei(@Param("imei") String imei);

    /**
     * 根据IMEI查询设备实体
     * @param imei 设备IMEI号
     * @return 设备实体
     */
    IotDevice selectDeviceByImei(@Param("imei") String imei);

    /**
     * 更新设备在线状态
     * @param imei 设备IMEI号
     * @param status 状态
     * @param clientIp 客户端IP
     */
    void updateDeviceStatus(@Param("imei") String imei, 
                           @Param("status") String status, 
                           @Param("clientIp") String clientIp);

    /**
     * 更新设备SIM卡信息
     * @param imei 设备IMEI号
     * @param imsi SIM卡IMSI号
     * @param iccid SIM卡ICCID号
     */
    void updateDeviceSimInfo(@Param("imei") String imei, 
                            @Param("imsi") String imsi, 
                            @Param("iccid") String iccid);

    /**
     * 获取设备统计信息
     * @return 统计信息
     */
    Object selectDeviceStatistics();
}
