package com.yunqu.park.iot.netty.handler;

import com.yunqu.park.common.core.utils.SpringUtils;
import com.yunqu.park.iot.constant.IotConstants;
import com.yunqu.park.iot.netty.codec.GT06ProtocolEncoder;
import com.yunqu.park.iot.netty.manager.DeviceConnectionManager;
import com.yunqu.park.iot.netty.protocol.IotMessage;
import com.yunqu.park.iot.service.IIotProtocolService;
import io.netty.buffer.Unpooled;
import io.netty.channel.ChannelHandlerContext;
import io.netty.channel.SimpleChannelInboundHandler;
import io.netty.handler.timeout.IdleStateEvent;
import io.netty.util.AttributeKey;
import lombok.extern.slf4j.Slf4j;

import java.time.Duration;
import java.time.Instant;

/**
 * IoT消息处理器
 *
 * <AUTHOR>
 */
@Slf4j
public class IotMessageHandler extends SimpleChannelInboundHandler<IotMessage> {


    // Channel属性键
    private static final AttributeKey<DisconnectInitiator> DISCONNECT_INITIATOR =
            AttributeKey.valueOf("disconnect_initiator");
    private static final AttributeKey<DisconnectReason> DISCONNECT_REASON =
            AttributeKey.valueOf("disconnect_reason");
    private static final AttributeKey<Instant> CONNECTION_START_TIME =
            AttributeKey.valueOf("connection_start_time");

    private IIotProtocolService protocolService;
    private DeviceConnectionManager connectionManager;

    @Override
    public void handlerAdded(ChannelHandlerContext ctx) throws Exception {
        super.handlerAdded(ctx);
        // 延迟获取Spring Bean，避免循环依赖
        if (protocolService == null) {
            protocolService = SpringUtils.getBean(IIotProtocolService.class);
        }
        if (connectionManager == null) {
            connectionManager = SpringUtils.getBean(DeviceConnectionManager.class);
        }
    }

    @Override
    protected void channelRead0(ChannelHandlerContext ctx, IotMessage msg) throws Exception {
        String remoteAddress = ctx.channel().remoteAddress().toString();
        String channelId = ctx.channel().id().asShortText();

        try {
            log.info("[MESSAGE-RECEIVED] 📨 接收到消息: RemoteAddress={}, ChannelId={}, Protocol=0x{}, IMEI={}, SequenceNumber={}",
                    remoteAddress, channelId, String.format("%02X", msg.getProtocol() & 0xFF),
                    msg.getImei(), msg.getSequenceNumber());

            log.debug("[MESSAGE-RECEIVED] 消息详情: {}", msg);

            // 阶段3增强：添加消息处理前的验证
            if (!validateMessage(msg)) {
                log.warn("[MESSAGE-RECEIVED] ⚠️ 消息验证失败，跳过处理: RemoteAddress={}, Protocol=0x{}",
                        remoteAddress, String.format("%02X", msg.getProtocol() & 0xFF));
                return;
            }

            // 处理协议消息
            handleProtocolMessage(ctx, msg);


            log.info("[MESSAGE-CHECK] Protocol:{}", msg.getProtocol());

            // 发送响应(如果需要)
            if (msg.needResponse()) {
                log.info("[MESSAGE-RESPONSE] Sending response for protocol: 0x{}",
                         String.format("%02X", msg.getProtocol() & 0xFF));
                sendResponse(ctx, msg);
            } else {
                log.info("[MESSAGE-RESPONSE] No response needed for protocol: 0x{}",
                         String.format("%02X", msg.getProtocol() & 0xFF));
            }

        } catch (Exception e) {
            log.error("[MESSAGE-ERROR] ❌ Error processing message: RemoteAddress={}, ChannelId={}, Protocol=0x{}, Error={}",
                     remoteAddress, channelId, String.format("%02X", msg.getProtocol() & 0xFF), e.getMessage(), e);
        }
    }

    /**
     * 处理协议消息
     * 修正：按照concox-master标准协议号进行处理
     */
    private void handleProtocolMessage(ChannelHandlerContext ctx, IotMessage msg) {
        try {
            byte protocol = msg.getProtocol();
            log.debug("[PROTOCOL-HANDLER] 处理协议消息: Protocol=0x{}, IMEI={}, RemoteAddress={}",
                     String.format("%02X", protocol & 0xFF), msg.getImei(), ctx.channel().remoteAddress());

            switch (protocol) {
                case IotConstants.GT06Protocol.PROTOCOL_LOGIN:
                    // 0x01 - 登录包
                    protocolService.handleLoginMessage(msg, ctx);
                    break;
                case IotConstants.GT06Protocol.PROTOCOL_LOCATION:
                    // 0x12 - 定位数据包（GPS/LBS合并）
                    protocolService.handleLocationMessage(msg, ctx);
                    break;

                case IotConstants.GT06Protocol.PROTOCOL_STATUS_INFO:
                    // 0x13 - 状态信息包（心跳包）
                    protocolService.handleStatusInfoMessage(msg, ctx);
                    break;

                case IotConstants.GT06Protocol.PROTOCOL_STRING_INFO:
                    // 0x15 - 予符串信息（回应服务端指令）
                    protocolService.handleTerminalResponseMessage(msg, ctx);
                    break;

                case IotConstants.GT06Protocol.PROTOCOL_ALARM_INFO:
                    // 0x16 - 报警信息
                    protocolService.handleAlarmInfoMessage(msg, ctx);
                    break;

                case IotConstants.GT06Protocol.PROTOCOL_LBS:
                    // 0x18 - LBS多基站信息
                    protocolService.handleLbsMessage(msg, ctx);
                    break;

                case IotConstants.GT06Protocol.PROTOCOL_ADDRESS_QUERY:
                    // 0x1A - 查询地址信息（GPS）
                    protocolService.handleAddressQueryMessage(msg, ctx);
                    break;

                case IotConstants.GT06Protocol.PROTOCOL_LBS_WIFI:
                    // 0x2C - LBS+WIFI信息
                    protocolService.handleLbsWifiMessage(msg, ctx);
                    break;

                case IotConstants.GT06Protocol.PROTOCOL_IMSI:
                    // 0x90 - IMSI号上报平台信息
                    protocolService.handleImsiMessage(msg, ctx);
                    break;

                case IotConstants.GT06Protocol.PROTOCOL_ICCID:
                    // 0x94 - ICCID号上报平台信息
                    protocolService.handleIccidMessage(msg, ctx);
                    break;

                case IotConstants.GT06Protocol.PROTOCOL_RECORD:
                    // 0x8D - 录音文件上报平台信息
                    protocolService.handleRecordMessage(msg, ctx);
                    break;

                case IotConstants.GT06Protocol.PROTOCOL_SERVER_CMD:
                    // 0x80 - 下发指令
                    // 此处通常由服务端主动发起，此处保留用于可能的上行确认
                    break;

                default:
                    log.warn("[PROTOCOL-HANDLER] ⚠️ 不支持的协议: Protocol=0x{}, RemoteAddress={}, 可能需要添加新的协议处理",
                            String.format("%02X", protocol & 0xFF), ctx.channel().remoteAddress());
                    // 记录未知协议用于后续分析
                    recordUnknownProtocol(protocol, msg, ctx);
            }
        } catch (Exception e) {
            log.error("[PROTOCOL-HANDLER] ❌ 协议消息处理异常: Protocol=0x{}, Error={}",
                     String.format("%02X", msg.getProtocol() & 0xFF), e.getMessage(), e);
        }
    }

    /**
     * 发送响应消息
     * 修正：按照concox-master标准协议号进行响应
     */
    private void sendResponse(ChannelHandlerContext ctx, IotMessage msg) {
        try {
            byte[] responseData = null;

            switch (msg.getProtocol()) {
                case IotConstants.GT06Protocol.PROTOCOL_LOGIN:
                    responseData = GT06ProtocolEncoder.buildLoginResponse(msg.getSequenceNumber());
                    log.info("[RESPONSE] 发送登录响应: Protocol=0x01, SequenceNumber={}", msg.getSequenceNumber());
                    break;
                case IotConstants.GT06Protocol.PROTOCOL_STATUS_INFO:
                    responseData = GT06ProtocolEncoder.buildStatusInfoResponse(msg.getSequenceNumber());
                    log.info("[RESPONSE] 发送状态信息响应（心跳包）: Protocol=0x13, SequenceNumber={}", msg.getSequenceNumber());
                    break;
                case IotConstants.GT06Protocol.PROTOCOL_RECORD:
                    // 录音协议包响应，使用报警响应格式
                    responseData = GT06ProtocolEncoder.buildAlarmResponse(msg.getSequenceNumber());
                    log.info("[RESPONSE] 发送录音协议响应: Protocol=0x8D, SequenceNumber={}", msg.getSequenceNumber());
                    break;
                default:
                    log.debug("[RESPONSE] 协议无需响应: Protocol=0x{}",
                            String.format("%02X", msg.getProtocol() & 0xFF));
                    return;
            }

            if (responseData != null) {
                // 输出完整的16进制响应内容
                String hexResponse = bytesToHexString(responseData);
                log.info("[RESPONSE-HEX] 📤 发送响应数据: Protocol=0x{}, SequenceNumber={}, RemoteAddress={}, Length={}字节",
                        String.format("%02X", msg.getProtocol() & 0xFF),
                        msg.getSequenceNumber(),
                        ctx.channel().remoteAddress(),
                        responseData.length);
                log.info("[RESPONSE-HEX] 完整16进制内容: {}", hexResponse);

                // 解析并显示响应包结构
                logResponsePacketStructure(responseData, msg.getProtocol());

                ctx.writeAndFlush(Unpooled.wrappedBuffer(responseData));
                log.info("[RESPONSE] ✅ 响应发送成功: Protocol=0x{}, SequenceNumber={}, RemoteAddress={}, ResponseLength={}",
                        String.format("%02X", msg.getProtocol() & 0xFF),
                        msg.getSequenceNumber(),
                        ctx.channel().remoteAddress(),
                        responseData.length);
            }

        } catch (Exception e) {
            log.error("[RESPONSE] ❌ 响应发送失败: Protocol=0x{}, Error={}",
                     String.format("%02X", msg.getProtocol() & 0xFF), e.getMessage(), e);
        }
    }

    @Override
    public void channelActive(ChannelHandlerContext ctx) throws Exception {
        String remoteAddress = ctx.channel().remoteAddress().toString();
        String channelId = ctx.channel().id().asShortText();

        // 记录连接建立时间
        ctx.channel().attr(CONNECTION_START_TIME).set(Instant.now());

        log.info("[CHANNEL-ACTIVE] 🔗 New device connection established: RemoteAddress={}, ChannelId={}",
                remoteAddress, channelId);
        log.debug("[CHANNEL-ACTIVE] Channel details: LocalAddress={}, IsActive={}, IsOpen={}",
                 ctx.channel().localAddress(), ctx.channel().isActive(), ctx.channel().isOpen());

        super.channelActive(ctx);
    }

    @Override
    public void channelInactive(ChannelHandlerContext ctx) throws Exception {
        String remoteAddress = ctx.channel().remoteAddress() != null ?
                              ctx.channel().remoteAddress().toString() : "unknown";
        String channelId = ctx.channel().id().asShortText();

        // 获取设备IMEI
        String imei = "unknown";
        if (connectionManager != null) {
            String deviceImei = connectionManager.getDeviceImei(ctx.channel());
            if (deviceImei != null) {
                imei = deviceImei;
            }
        }

        // 确定连接断开的主动方和原因
        DisconnectInitiator initiator = ctx.channel().hasAttr(DISCONNECT_INITIATOR) ?
                ctx.channel().attr(DISCONNECT_INITIATOR).get() : DisconnectInitiator.CLIENT;

        DisconnectReason reason = ctx.channel().hasAttr(DISCONNECT_REASON) ?
                ctx.channel().attr(DISCONNECT_REASON).get() : DisconnectReason.CLIENT_CLOSED;

        // 计算连接持续时间
        String connectionDuration = "未知";
        if (ctx.channel().hasAttr(CONNECTION_START_TIME)) {
            Instant startTime = ctx.channel().attr(CONNECTION_START_TIME).get();
            Duration duration = Duration.between(startTime, Instant.now());
            long seconds = duration.getSeconds();

            if (seconds < 60) {
                connectionDuration = seconds + "秒";
            } else if (seconds < 3600) {
                connectionDuration = (seconds / 60) + "分" + (seconds % 60) + "秒";
            } else {
                connectionDuration = (seconds / 3600) + "小时" + ((seconds % 3600) / 60) + "分" + (seconds % 60) + "秒";
            }
        }

        // 根据不同的断开原因记录不同级别的日志
        if (initiator == DisconnectInitiator.CLIENT) {
            log.info("[CHANNEL-INACTIVE] 🔌 设备连接断开: 主动方={}, 原因={}, IMEI={}, RemoteAddress={}, ChannelId={}, 连接持续时间={}",
                    initiator.getDescription(), reason.getDescription(), imei, remoteAddress, channelId, connectionDuration);
        } else {
            // 服务端主动断开通常是因为异常或超时，使用警告级别
            log.warn("[CHANNEL-INACTIVE] 🔌 设备连接断开: 主动方={}, 原因={}, IMEI={}, RemoteAddress={}, ChannelId={}, 连接持续时间={}",
                    initiator.getDescription(), reason.getDescription(), imei, remoteAddress, channelId, connectionDuration);
        }

        // 从连接管理器中移除设备
        if (connectionManager != null) {
            if (imei != null && !imei.equals("unknown")) {
                log.info("[CHANNEL-INACTIVE] 从连接管理器移除设备: IMEI={}", imei);
            }
            connectionManager.removeDeviceByChannel(ctx.channel());
        }

        // 处理设备离线
        if (protocolService != null) {
            log.debug("[CHANNEL-INACTIVE] 通过协议服务处理设备断开连接");
            protocolService.handleDeviceDisconnected(ctx);
        }

        log.info("[CHANNEL-INACTIVE] ✅ 设备断开连接处理完成: IMEI={}, RemoteAddress={}, ChannelId={}",
                imei, remoteAddress, channelId);

        super.channelInactive(ctx);
    }

    @Override
    public void userEventTriggered(ChannelHandlerContext ctx, Object evt) throws Exception {
        if (evt instanceof IdleStateEvent) {
            IdleStateEvent event = (IdleStateEvent) evt;
            String remoteAddress = ctx.channel().remoteAddress() != null ?
                                  ctx.channel().remoteAddress().toString() : "unknown";
            String channelId = ctx.channel().id().asShortText();
            String imei = connectionManager != null ?
                         connectionManager.getDeviceImei(ctx.channel()) : "unknown";

            switch (event.state()) {
                case READER_IDLE:
                    // 读空闲：客户端长时间没有发送数据
                    log.warn("[IDLE-EVENT] 📖 读空闲超时: IMEI={}, RemoteAddress={}, ChannelId={}, IdleTime=300s",
                            imei, remoteAddress, channelId);
                    log.warn("[IDLE-EVENT] 客户端3分钟心跳，5分钟未收到数据，设备可能离线，准备关闭连接");
                    handleReaderIdle(ctx, imei);
                    break;

                case WRITER_IDLE:
                    // 写空闲：服务器长时间没有发送数据
                    log.info("[IDLE-EVENT] ✍️ 写空闲检测: IMEI={}, RemoteAddress={}, ChannelId={}",
                            imei, remoteAddress, channelId);
                    // 服务器是被动响应的，写空闲是正常的，不需要特殊处理
                    break;

                case ALL_IDLE:
                    // 全部空闲：读写都长时间没有活动
                    log.warn("[IDLE-EVENT] 💤 全部空闲超时: IMEI={}, RemoteAddress={}, ChannelId={}, IdleTime=300s",
                            imei, remoteAddress, channelId);
                    log.warn("[IDLE-EVENT] 设备长时间无任何活动，强制关闭连接");
                    handleAllIdle(ctx, imei);
                    break;
            }
        } else {
            super.userEventTriggered(ctx, evt);
        }
    }

    /**
     * 处理读空闲事件
     */
    private void handleReaderIdle(ChannelHandlerContext ctx, String imei) {
        try {
            log.info("[IDLE-HANDLER] 处理读空闲: IMEI={}", imei);

            // 设置断开连接的主动方和原因
            ctx.channel().attr(DISCONNECT_INITIATOR).set(DisconnectInitiator.SERVER);
            ctx.channel().attr(DISCONNECT_REASON).set(DisconnectReason.READER_IDLE_TIMEOUT);

            // 记录设备离线事件
            if (protocolService != null) {
                protocolService.handleDeviceDisconnected(ctx);
            }

            // 优雅关闭连接
            ctx.close().addListener(future -> {
                if (future.isSuccess()) {
                    log.info("[IDLE-HANDLER] ✅ 读空闲连接关闭成功: IMEI={}", imei);
                } else {
                    log.error("[IDLE-HANDLER] ❌ 读空闲连接关闭失败: IMEI={}, Error={}",
                             imei, future.cause() != null ? future.cause().getMessage() : "unknown");
                }
            });

        } catch (Exception e) {
            log.error("[IDLE-HANDLER] 处理读空闲异常: IMEI={}, Error={}", imei, e.getMessage(), e);
            ctx.close();
        }
    }

    /**
     * 处理全部空闲事件
     */
    private void handleAllIdle(ChannelHandlerContext ctx, String imei) {
        try {
            log.info("[IDLE-HANDLER] 处理全部空闲: IMEI={}", imei);

            // 设置断开连接的主动方和原因
            ctx.channel().attr(DISCONNECT_INITIATOR).set(DisconnectInitiator.SERVER);
            ctx.channel().attr(DISCONNECT_REASON).set(DisconnectReason.ALL_IDLE_TIMEOUT);

            // 记录设备长时间无活动事件
            if (protocolService != null) {
                protocolService.handleDeviceDisconnected(ctx);
            }

            // 强制关闭连接
            ctx.close().addListener(future -> {
                if (future.isSuccess()) {
                    log.info("[IDLE-HANDLER] ✅ 全部空闲连接关闭成功: IMEI={}", imei);
                } else {
                    log.error("[IDLE-HANDLER] ❌ 全部空闲连接关闭失败: IMEI={}, Error={}",
                             imei, future.cause() != null ? future.cause().getMessage() : "unknown");
                }
            });

        } catch (Exception e) {
            log.error("[IDLE-HANDLER] 处理全部空闲异常: IMEI={}, Error={}", imei, e.getMessage(), e);
            ctx.close();
        }
    }

    @Override
    public void exceptionCaught(ChannelHandlerContext ctx, Throwable cause) throws Exception {
        String remoteAddress = ctx.channel().remoteAddress() != null ?
                              ctx.channel().remoteAddress().toString() : "unknown";
        String channelId = ctx.channel().id().asShortText();

        // 阶段3增强：分类处理不同类型的异常
        if (cause instanceof java.io.IOException) {
            log.warn("[CHANNEL-EXCEPTION] 🔌 网络IO异常: RemoteAddress={}, ChannelId={}, Error={}",
                    remoteAddress, channelId, cause.getMessage());

            // 设置断开连接的主动方和原因
            ctx.channel().attr(DISCONNECT_INITIATOR).set(DisconnectInitiator.SERVER);
            ctx.channel().attr(DISCONNECT_REASON).set(DisconnectReason.NETWORK_ERROR);

            handleNetworkException(ctx, cause);
        } else if (cause instanceof IllegalArgumentException) {
            log.warn("[CHANNEL-EXCEPTION] ⚠️ 协议解析异常: RemoteAddress={}, ChannelId={}, Error={}",
                    remoteAddress, channelId, cause.getMessage());

            // 设置断开连接的主动方和原因
            ctx.channel().attr(DISCONNECT_INITIATOR).set(DisconnectInitiator.SERVER);
            ctx.channel().attr(DISCONNECT_REASON).set(DisconnectReason.PROTOCOL_ERROR);

            handleProtocolException(ctx, cause);
        } else if (cause instanceof OutOfMemoryError) {
            log.error("[CHANNEL-EXCEPTION] 💥 内存不足异常: RemoteAddress={}, ChannelId={}, Error={}",
                    remoteAddress, channelId, cause.getMessage());

            // 设置断开连接的主动方和原因
            ctx.channel().attr(DISCONNECT_INITIATOR).set(DisconnectInitiator.SERVER);
            ctx.channel().attr(DISCONNECT_REASON).set(DisconnectReason.MEMORY_ERROR);

            handleMemoryException(ctx, cause);
        } else {
            log.error("[CHANNEL-EXCEPTION] ❌ 未知异常: RemoteAddress={}, ChannelId={}, Exception={}",
                    remoteAddress, channelId, cause.getClass().getSimpleName());
            log.error("[CHANNEL-EXCEPTION] 异常详情: {}", cause.getMessage(), cause);

            // 设置断开连接的主动方和原因
            ctx.channel().attr(DISCONNECT_INITIATOR).set(DisconnectInitiator.SERVER);
            ctx.channel().attr(DISCONNECT_REASON).set(DisconnectReason.EXCEPTION);

            handleUnknownException(ctx, cause);
        }

        // 获取设备IMEI用于日志追踪
        if (connectionManager != null) {
            String imei = connectionManager.getDeviceImei(ctx.channel());
            if (imei != null) {
                log.error("[CHANNEL-EXCEPTION] 受影响的设备IMEI: {}", imei);
            }
        }

        // 阶段3增强：确保资源清理
        cleanupChannelResources(ctx);
    }

    /**
     * 阶段3增强：消息验证方法
     * @param message 待验证的消息
     * @return 验证是否通过
     */
    private boolean validateMessage(IotMessage message) {
        if (message == null) {
            log.warn("[MESSAGE-VALIDATE] 消息对象为空");
            return false;
        }

        // 验证协议号
        if (message.getProtocol() == 0) {
            log.warn("[MESSAGE-VALIDATE] 协议号无效: {}", message.getProtocol());
            return false;
        }

        // 验证序列号
        if (message.getSequenceNumber() < 0) {
            log.warn("[MESSAGE-VALIDATE] 序列号无效: {}", message.getSequenceNumber());
            return false;
        }

        // 对于登录包，验证IMEI
        if (message.getProtocol() == IotConstants.GT06Protocol.PROTOCOL_LOGIN) {
            if (message.getImei() == null || message.getImei().trim().isEmpty()) {
                log.warn("[MESSAGE-VALIDATE] 登录包IMEI为空");
                return false;
            }
            if (message.getImei().length() < 15) {
                log.warn("[MESSAGE-VALIDATE] IMEI长度不足: {}", message.getImei());
                return false;
            }
        }

        return true;
    }

    /**
     * 阶段3增强：处理网络异常
     */
    private void handleNetworkException(ChannelHandlerContext ctx, Throwable cause) {
        log.debug("[EXCEPTION-HANDLER] 处理网络异常，正常关闭连接");
        // 网络异常通常是客户端断开连接，正常处理即可
    }

    /**
     * 阶段3增强：处理协议异常
     */
    private void handleProtocolException(ChannelHandlerContext ctx, Throwable cause) {
        log.debug("[EXCEPTION-HANDLER] 处理协议异常，继续保持连接");
        // 协议异常不关闭连接，给客户端重新发送的机会
    }

    /**
     * 阶段3增强：处理内存异常
     */
    private void handleMemoryException(ChannelHandlerContext ctx, Throwable cause) {
        log.error("[EXCEPTION-HANDLER] 内存不足，立即关闭连接并清理资源");
        // 内存异常需要立即关闭连接
        ctx.close();
    }

    /**
     * 阶段3增强：处理未知异常
     */
    private void handleUnknownException(ChannelHandlerContext ctx, Throwable cause) {
        log.error("[EXCEPTION-HANDLER] 未知异常，关闭连接");
        ctx.close();
    }

    /**
     * 阶段3增强：清理通道资源
     */
    private void cleanupChannelResources(ChannelHandlerContext ctx) {
        try {
            // 从连接管理器移除设备
            if (connectionManager != null) {
                connectionManager.removeDeviceByChannel(ctx.channel());
            }

            // 清理通道属性
            ctx.channel().attr(io.netty.util.AttributeKey.valueOf("device_imei")).set(null);

            log.debug("[EXCEPTION-HANDLER] 通道资源清理完成");

        } catch (Exception e) {
            log.error("[EXCEPTION-HANDLER] 清理通道资源时发生异常: {}", e.getMessage(), e);
        }
    }

    /**
     * 记录未知协议，用于后续分析和扩展
     */
    private void recordUnknownProtocol(byte protocol, IotMessage msg, ChannelHandlerContext ctx) {
        try {
            String remoteAddress = ctx.channel().remoteAddress().toString();
            String hexData = msg.getContent() != null ?
                           java.util.HexFormat.of().formatHex(msg.getContent()) : "null";

            log.warn("[UNKNOWN-PROTOCOL] 📊 未知协议记录:");
            log.warn("  协议号: 0x{}", String.format("%02X", protocol & 0xFF));
            log.warn("  远程地址: {}", remoteAddress);
            log.warn("  序列号: {}", msg.getSequenceNumber());
            log.warn("  数据长度: {}", msg.getContent() != null ? msg.getContent().length : 0);
            log.warn("  数据内容: {}", hexData);
            log.warn("  IMEI: {}", msg.getImei());

            // TODO: 可以将未知协议数据保存到数据库或文件，用于后续分析
            // unknownProtocolService.saveUnknownProtocol(protocol, msg, remoteAddress);

        } catch (Exception e) {
            log.error("[UNKNOWN-PROTOCOL] 记录未知协议时发生异常: {}", e.getMessage(), e);
        }
    }

    /**
     * 将字节数组转换为十六进制字符串
     * @param bytes 字节数组
     * @return 十六进制字符串
     */
    private String bytesToHexString(byte[] bytes) {
        if (bytes == null || bytes.length == 0) {
            return "";
        }
        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < bytes.length; i++) {
            if (i > 0) {
                sb.append(" ");
            }
            sb.append(String.format("%02X", bytes[i] & 0xFF));
        }
        return sb.toString();
    }

    /**
     * 解析并记录响应包结构
     * @param responseData 响应数据
     * @param protocol 协议号
     */
    private void logResponsePacketStructure(byte[] responseData, byte protocol) {
        try {
            if (responseData == null || responseData.length < 10) {
                log.warn("[RESPONSE-STRUCTURE] 响应包数据不完整，长度: {}",
                        responseData != null ? responseData.length : 0);
                return;
            }

            log.info("[RESPONSE-STRUCTURE] 📋 响应包结构解析:");
            log.info("  起始位: {} {} (0x7878)",
                    String.format("%02X", responseData[0] & 0xFF),
                    String.format("%02X", responseData[1] & 0xFF));

            log.info("  包长度: {} ({}字节)",
                    String.format("%02X", responseData[2] & 0xFF),
                    responseData[2] & 0xFF);

            log.info("  协议号: {} ({})",
                    String.format("%02X", responseData[3] & 0xFF),
                    getProtocolName(protocol));

            // 解析序列号（倒数第6和第5字节）
            if (responseData.length >= 8) {
                int sequenceNumber = ((responseData[responseData.length - 6] & 0xFF) << 8) |
                                   (responseData[responseData.length - 5] & 0xFF);
                log.info("  序列号: {} {} ({})",
                        String.format("%02X", responseData[responseData.length - 6] & 0xFF),
                        String.format("%02X", responseData[responseData.length - 5] & 0xFF),
                        sequenceNumber);
            }

            // 解析CRC（倒数第4和第3字节）
            if (responseData.length >= 6) {
                log.info("  CRC校验: {} {}",
                        String.format("%02X", responseData[responseData.length - 4] & 0xFF),
                        String.format("%02X", responseData[responseData.length - 3] & 0xFF));
            }

            log.info("  停止位: {} {} (0x0D0A)",
                    String.format("%02X", responseData[responseData.length - 2] & 0xFF),
                    String.format("%02X", responseData[responseData.length - 1] & 0xFF));

            // 如果有内容数据，显示内容部分
            int contentStart = 4;
            int contentEnd = responseData.length - 6;
            if (contentEnd > contentStart) {
                StringBuilder contentHex = new StringBuilder();
                for (int i = contentStart; i < contentEnd; i++) {
                    if (i > contentStart) {
                        contentHex.append(" ");
                    }
                    contentHex.append(String.format("%02X", responseData[i] & 0xFF));
                }
                log.info("  内容数据: {} ({}字节)", contentHex.toString(), contentEnd - contentStart);

            } else {
                log.info("  内容数据: 无");
            }

        } catch (Exception e) {
            log.error("[RESPONSE-STRUCTURE] 解析响应包结构异常: {}", e.getMessage(), e);
        }
    }

    /**
     * 获取协议名称
     * @param protocol 协议号
     * @return 协议名称
     */
    private String getProtocolName(byte protocol) {
        return switch (protocol & 0xFF) {
            case 0x01 -> "登录响应";
            case 0x13 -> "状态信息响应（心跳包）";
            case 0x8D -> "录音协议响应";
            default -> "未知协议(" + String.format("0x%02X", protocol & 0xFF) + ")";
        };
    }
}
