package com.yunqu.park.iot.model.command.dto;

import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = false)
public class SetDualIpParams extends CommandParams {
    @NotNull(message = "模式不能为空")
    @Min(value = 0, message = "模式必须为0或1")
    @Max(value = 1, message = "模式必须为0或1")
    private Integer mode;

    @NotBlank(message = "服务器地址1不能为空")
    private String addr1;

    @NotNull(message = "端口1不能为空")
    private Integer port1;
    
    @NotBlank(message = "服务器地址2不能为空")
    private String addr2;

    @NotNull(message = "端口2不能为空")
    private Integer port2;
}