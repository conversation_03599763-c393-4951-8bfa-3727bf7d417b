package com.yunqu.park.iot.service.command.impl;

import com.yunqu.park.iot.model.command.CommandType;
import com.yunqu.park.iot.model.command.dto.SetAlarmParams;
import com.yunqu.park.iot.service.command.ICommand;
import lombok.RequiredArgsConstructor;

@RequiredArgsConstructor
public class SetAccAlarmCommand implements ICommand {

    private final SetAlarmParams params;
    private static final String TEMPLATE = "ACCALM,%s,%d#";

    @Override
    public CommandType getType() {
        return CommandType.SET_ACC_ALARM;
    }

    @Override
    public String build() {
        return String.format(TEMPLATE, params.getState(), params.getMode());
    }

    @Override
    public String getDescription() {
        return String.format("%s: 状态=%s, 模式=%d", getType().getDescription(), params.getState(), params.getMode());
    }
}