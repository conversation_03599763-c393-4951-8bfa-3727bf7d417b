package com.yunqu.park.iot.service.command.impl;

import com.yunqu.park.iot.model.command.CommandType;
import com.yunqu.park.iot.model.command.dto.SetSpeedMeasurementModeParams;
import com.yunqu.park.iot.service.command.ICommand;
import lombok.RequiredArgsConstructor;

@RequiredArgsConstructor
public class SetSpeedMeasurementModeCommand implements ICommand {

    private final SetSpeedMeasurementModeParams params;
    private static final String TEMPLATE = "SETCNMP,%d#";

    @Override
    public CommandType getType() {
        return CommandType.SET_SPEED_MEASUREMENT_MODE;
    }

    @Override
    public String build() {
        return String.format(TEMPLATE, params.getMode());
    }

    @Override
    public String getDescription() {
        return String.format("%s: 模式=%d", getType().getDescription(), params.getMode());
    }
}