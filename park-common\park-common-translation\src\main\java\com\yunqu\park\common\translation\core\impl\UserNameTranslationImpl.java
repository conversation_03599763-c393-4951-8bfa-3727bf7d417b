package com.yunqu.park.common.translation.core.impl;

import com.yunqu.park.common.core.service.UserService;
import com.yunqu.park.common.translation.annotation.TranslationType;
import com.yunqu.park.common.translation.constant.TransConstant;
import com.yunqu.park.common.translation.core.TranslationInterface;
import lombok.AllArgsConstructor;

/**
 * 用户名翻译实现
 *
 * <AUTHOR>
 */
@AllArgsConstructor
@TranslationType(type = TransConstant.USER_ID_TO_NAME)
public class UserNameTranslationImpl implements TranslationInterface<String> {

    private final UserService userService;

    @Override
    public String translation(Object key, String other) {
        if (key instanceof Long id) {
            return userService.selectUserNameById(id);
        }
        return null;
    }
}
