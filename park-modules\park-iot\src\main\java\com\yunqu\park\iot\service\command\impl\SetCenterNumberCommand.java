package com.yunqu.park.iot.service.command.impl;

import com.yunqu.park.iot.model.command.CommandType;
import com.yunqu.park.iot.model.command.dto.SetCenterNumberParams;
import com.yunqu.park.iot.service.command.ICommand;
import lombok.RequiredArgsConstructor;

@RequiredArgsConstructor
public class SetCenterNumberCommand implements ICommand {

    private final SetCenterNumberParams params;
    private static final String TEMPLATE = "CENTER,A,%s#";

    @Override
    public CommandType getType() {
        return CommandType.SET_CENTER_NUMBER;
    }

    @Override
    public String build() {
        return String.format(TEMPLATE, params.getPhone());
    }

    @Override
    public String getDescription() {
        return String.format("%s: %s", getType().getDescription(), params.getPhone());
    }
}