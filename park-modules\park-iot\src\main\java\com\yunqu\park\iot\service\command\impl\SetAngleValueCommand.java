package com.yunqu.park.iot.service.command.impl;

import com.yunqu.park.iot.model.command.CommandType;
import com.yunqu.park.iot.model.command.dto.SetAngleValueParams;
import com.yunqu.park.iot.service.command.ICommand;
import lombok.RequiredArgsConstructor;

@RequiredArgsConstructor
public class SetAngleValueCommand implements ICommand {

    private final SetAngleValueParams params;
    private static final String TEMPLATE = "ANGLEVAL,%d#";

    @Override
    public CommandType getType() {
        return CommandType.SET_ANGLE_VALUE;
    }

    @Override
    public String build() {
        return String.format(TEMPLATE, params.getAngle());
    }

    @Override
    public String getDescription() {
        return String.format("%s: %d度", getType().getDescription(), params.getAngle());
    }
}