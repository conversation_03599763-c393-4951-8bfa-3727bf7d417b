package com.yunqu.park.iot.model.dto.command;

import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 设置GPRS开关请求
 * 
 * <AUTHOR>
 * @since 2025-01-06
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class SetGprsSwitchRequest extends BaseCommandReq {
    
    /**
     * 是否启用GPRS
     * true: 启用, false: 禁用
     */
    @NotNull(message = "GPRS开关不能为空")
    private Boolean enabled;
}