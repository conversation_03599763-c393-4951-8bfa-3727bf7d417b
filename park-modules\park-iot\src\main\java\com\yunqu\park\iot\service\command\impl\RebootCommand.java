package com.yunqu.park.iot.service.command.impl;

import com.yunqu.park.iot.model.command.CommandType;
import com.yunqu.park.iot.service.command.ICommand;
import lombok.RequiredArgsConstructor;

/**
 * 设备重启指令实现
 *
 * <p>实现设备远程重启功能，发送RESET#指令使设备执行软重启操作。
 * 这是一个无参数指令，执行后设备将重新启动并重新建立网络连接。</p>
 *
 * <h3>指令特点：</h3>
 * <ul>
 *   <li><strong>无参数</strong>：不需要任何额外参数</li>
 *   <li><strong>立即执行</strong>：设备收到指令后立即执行重启</li>
 *   <li><strong>连接中断</strong>：重启过程中设备会暂时离线</li>
 *   <li><strong>自动重连</strong>：重启完成后设备会自动重新连接服务器</li>
 * </ul>
 *
 * <h3>使用场景：</h3>
 * <ul>
 *   <li>设备出现异常状态需要重置</li>
 *   <li>配置更新后需要重启生效</li>
 *   <li>定期维护重启保持设备稳定性</li>
 *   <li>远程故障排除的第一步操作</li>
 * </ul>
 *
 * <h3>注意事项：</h3>
 * <ul>
 *   <li>重启过程中设备会暂时无法响应其他指令</li>
 *   <li>重启时间通常为30-60秒，具体取决于设备型号</li>
 *   <li>频繁重启可能影响设备寿命，建议合理使用</li>
 *   <li>重启后设备状态会重置为默认值</li>
 * </ul>
 *
 * <h3>协议格式：</h3>
 * <pre>{@code
 * 发送: "RESET#"
 * 响应: 设备重启，连接断开后重新建立
 * }</pre>
 *
 * <AUTHOR>
 * @version 2.0
 * @since 2025-01-06
 * @see com.yunqu.park.iot.model.command.CommandType#REBOOT
 */
@RequiredArgsConstructor
public class RebootCommand implements ICommand {

    /** 重启指令模板 */
    private static final String TEMPLATE = "RESET#";

    @Override
    public CommandType getType() {
        return CommandType.REBOOT;
    }

    @Override
    public String build() {
        return TEMPLATE;
    }

    @Override
    public String getDescription() {
        return getType().getDescription();
    }
}