package com.yunqu.park.iot.model.command.dto;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = false)
public class SetMovingAlarmParams extends CommandParams {
    @NotBlank(message = "状态不能为空 (ON/OFF)")
    private String state;

    @NotNull(message = "位移半径不能为空")
    private Integer radius;

    @NotNull(message = "报警模式不能为空")
    private Integer mode;
}