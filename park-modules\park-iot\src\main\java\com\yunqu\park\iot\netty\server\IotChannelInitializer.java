package com.yunqu.park.iot.netty.server;

import com.yunqu.park.iot.netty.codec.GT06ProtocolDecoder;
import com.yunqu.park.iot.netty.codec.GT06ProtocolEncoder;
import com.yunqu.park.iot.netty.handler.IotMessageHandler;
import com.yunqu.park.iot.netty.handler.ServerShutdownHandler;
import io.netty.channel.ChannelInitializer;
import io.netty.channel.ChannelPipeline;
import io.netty.channel.socket.SocketChannel;
import io.netty.handler.timeout.IdleStateHandler;
import lombok.extern.slf4j.Slf4j;

import java.util.concurrent.TimeUnit;

/**
 * IoT通道初始化器
 *
 * <AUTHOR>
 */
@Slf4j
public class IotChannelInitializer extends ChannelInitializer<SocketChannel> {

    /**
     * 心跳超时时间(秒)
     */
    private final int heartbeatTimeout;

    public IotChannelInitializer(int heartbeatTimeout) {
        this.heartbeatTimeout = heartbeatTimeout;
    }

    @Override
    protected void initChannel(SocketChannel ch) throws Exception {
        ChannelPipeline pipeline = ch.pipeline();

        // 空闲状态检测 - 用于心跳超时检测
        // 客户端3分钟(180秒)发送心跳，服务端设置5分钟(300秒)读空闲超时
        // 这样可以容忍1-2次心跳丢失，提高连接稳定性
        pipeline.addLast("idleStateHandler", new IdleStateHandler(
                300,     // 读空闲：300秒没收到数据触发READER_IDLE (客户端180秒心跳 + 120秒容错)
                0,       // 写空闲：0表示不检测写空闲（服务器是被动响应的）
                0,       // 全部空闲：0表示不检测全部空闲（只关注读空闲即可）
                TimeUnit.SECONDS));

        // 服务器关闭事件处理器 - 用于处理服务器关闭时的连接断开
        pipeline.addLast("shutdownHandler", new ServerShutdownHandler());

        // GT06协议解码器
        pipeline.addLast("decoder", new GT06ProtocolDecoder());

        // GT06协议编码器
        pipeline.addLast("encoder", new GT06ProtocolEncoder());

        // 业务消息处理器
        pipeline.addLast("handler", new IotMessageHandler());

        log.debug("Channel initialized for {}", ch.remoteAddress());
    }
}
