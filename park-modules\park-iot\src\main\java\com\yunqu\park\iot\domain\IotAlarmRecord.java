package com.yunqu.park.iot.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.yunqu.park.common.tenant.core.TenantEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 设备报警记录对象 iot_alarm_record
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("iot_alarm_record")
public class IotAlarmRecord extends TenantEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 报警记录ID
     */
    @TableId(value = "alarm_id")
    private Long alarmId;

    /**
     * 设备ID
     */
    private Long deviceId;

    /**
     * 设备IMEI
     */
    private String imei;

    /**
     * 报警类型
     */
    private Integer alarmType;

    /**
     * 报警名称
     */
    private String alarmName;

    /**
     * 报警描述
     */
    private String alarmDescription;

    /**
     * 报警时间
     */
    private Date alarmTime;

    /**
     * 报警位置纬度
     */
    private BigDecimal latitude;

    /**
     * 报警位置经度
     */
    private BigDecimal longitude;

    /**
     * 报警地址
     */
    private String address;

    /**
     * 处理状态:0-未处理,1-已处理
     */
    private String alarmStatus;

    /**
     * 处理时间
     */
    private Date handleTime;

    /**
     * 处理人
     */
    private Long handleUser;

    /**
     * 处理备注
     */
    private String handleRemark;

}
