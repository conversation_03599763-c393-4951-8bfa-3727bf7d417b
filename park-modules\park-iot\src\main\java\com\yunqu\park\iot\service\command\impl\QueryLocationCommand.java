package com.yunqu.park.iot.service.command.impl;

import com.yunqu.park.iot.model.command.CommandType;
import com.yunqu.park.iot.service.command.ICommand;
import lombok.RequiredArgsConstructor;

/**
 * 查询设备位置指令实现
 *
 * <p>实现设备实时位置查询功能，主动请求设备上报当前的GPS定位信息。
 * 该指令用于获取设备的精确位置数据，包括经纬度、速度、方向等信息。</p>
 *
 * <h3>指令特点：</h3>
 * <ul>
 *   <li><strong>无参数</strong>：不需要任何额外参数</li>
 *   <li><strong>实时查询</strong>：获取设备当前最新的位置信息</li>
 *   <li><strong>主动上报</strong>：设备收到指令后立即上报位置数据</li>
 *   <li><strong>GPS依赖</strong>：需要设备GPS模块正常工作</li>
 * </ul>
 *
 * <h3>返回数据包含：</h3>
 * <ul>
 *   <li><strong>经纬度坐标</strong>：WGS84坐标系的精确位置</li>
 *   <li><strong>定位时间</strong>：GPS定位的UTC时间戳</li>
 *   <li><strong>速度信息</strong>：当前行驶速度（km/h）</li>
 *   <li><strong>方向角度</strong>：行驶方向（0-360度）</li>
 *   <li><strong>卫星数量</strong>：当前接收到的GPS卫星数</li>
 *   <li><strong>定位精度</strong>：HDOP值，反映定位精度</li>
 * </ul>
 *
 * <h3>使用场景：</h3>
 * <ul>
 *   <li>车辆实时位置监控和调度</li>
 *   <li>紧急情况下的快速定位</li>
 *   <li>轨迹回放的关键点补充</li>
 *   <li>设备GPS功能测试验证</li>
 *   <li>地理围栏触发后的位置确认</li>
 * </ul>
 *
 * <h3>定位精度说明：</h3>
 * <ul>
 *   <li><strong>开阔地带</strong>：精度通常在3-5米范围内</li>
 *   <li><strong>城市环境</strong>：受建筑物影响，精度5-15米</li>
 *   <li><strong>隧道/地下</strong>：可能无法获取GPS信号</li>
 *   <li><strong>恶劣天气</strong>：可能影响定位精度和速度</li>
 * </ul>
 *
 * <h3>注意事项：</h3>
 * <ul>
 *   <li>设备需要在GPS信号覆盖区域内</li>
 *   <li>首次定位可能需要30-60秒的冷启动时间</li>
 *   <li>频繁查询位置会增加设备功耗</li>
 *   <li>室内或信号遮挡区域可能无法定位</li>
 * </ul>
 *
 * <h3>协议格式：</h3>
 * <pre>{@code
 * 发送: "WHERE#"
 * 响应: 设备上报包含位置信息的数据包
 * 格式: 经度,纬度,速度,方向,时间,卫星数等
 * }</pre>
 *
 * <AUTHOR>
 * @version 2.0
 * @since 2025-01-06
 * @see com.yunqu.park.iot.model.command.CommandType#QUERY_LOCATION
 */
@RequiredArgsConstructor
public class QueryLocationCommand implements ICommand {

    /** 位置查询指令模板 */
    private static final String TEMPLATE = "WHERE#";

    @Override
    public CommandType getType() {
        return CommandType.QUERY_LOCATION;
    }

    @Override
    public String build() {
        return TEMPLATE;
    }

    @Override
    public String getDescription() {
        return getType().getDescription();
    }
}