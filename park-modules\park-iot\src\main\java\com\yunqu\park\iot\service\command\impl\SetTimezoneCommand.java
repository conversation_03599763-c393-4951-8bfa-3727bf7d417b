package com.yunqu.park.iot.service.command.impl;

import com.yunqu.park.iot.model.command.CommandType;
import com.yunqu.park.iot.model.command.dto.SetTimezoneParams;
import com.yunqu.park.iot.service.command.ICommand;
import lombok.RequiredArgsConstructor;

@RequiredArgsConstructor
public class SetTimezoneCommand implements ICommand {

    private final SetTimezoneParams params;
    private static final String TEMPLATE = "GMT,%s,%d,%d#";

    @Override
    public CommandType getType() {
        return CommandType.SET_TIMEZONE;
    }

    @Override
    public String build() {
        return String.format(TEMPLATE, params.getDirection(), params.getHours(), params.getMinutes());
    }

    @Override
    public String getDescription() {
        return String.format("%s: %s%d 小时 %d 分钟",
            getType().getDescription(), params.getDirection(), params.getHours(), params.getMinutes());
    }
}