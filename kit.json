{"name": "我的项目-ruoyi-vue-plus", "space": "ruoyi", "service": {"ruoyi-vue-plus": {"version": "5.3.1", "variables": [{"name": "projectName", "inputType": "input", "value": "park-manager-system"}, {"name": "basePackage", "inputType": "input", "value": "com.yunqu.park"}, {"name": "projectNamePrefix", "inputType": "input", "value": "park"}, {"name": "database", "inputType": "datasource", "value": "2AX7BGJHAQQSO8"}, {"name": "redisPassword", "inputType": "input", "value": "foobared"}, {"name": "author", "inputType": "input", "value": "ruoyi"}, {"name": "basePackagePath", "inputType": "input", "value": "${basePackage?replace('.', '/')}"}, {"name": "lowerProjectName", "inputType": "input", "value": "${projectName?lower_case}"}, {"name": "_plugins", "inputType": "_plugins", "value": []}]}}, "plugins": {}}