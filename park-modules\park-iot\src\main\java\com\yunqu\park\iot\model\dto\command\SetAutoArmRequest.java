package com.yunqu.park.iot.model.dto.command;

import com.yunqu.park.iot.model.command.dto.SetAutoArmParams;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 设置自动布防指令请求
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "设置自动布防指令请求")
public class SetAutoArmRequest extends BaseCommandReq {

    @Schema(description = "自动布防参数")
    private SetAutoArmParams params;
}