package com.yunqu.park.iot.model.dto.command;

import com.yunqu.park.iot.model.command.dto.SetAngleUploadParams;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 设置角度上传指令请求
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "设置角度上传指令请求")
public class SetAngleUploadRequest extends BaseCommandReq {

    @Schema(description = "角度上传参数")
    private SetAngleUploadParams params;
}