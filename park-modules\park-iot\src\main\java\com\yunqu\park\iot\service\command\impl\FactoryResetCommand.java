package com.yunqu.park.iot.service.command.impl;

import com.yunqu.park.iot.model.command.CommandType;
import com.yunqu.park.iot.service.command.ICommand;
import lombok.RequiredArgsConstructor;

@RequiredArgsConstructor
public class FactoryResetCommand implements ICommand {

    private static final String TEMPLATE = "FACTORY#";

    @Override
    public CommandType getType() {
        return CommandType.FACTORY_RESET;
    }

    @Override
    public String build() {
        return TEMPLATE;
    }

    @Override
    public String getDescription() {
        return getType().getDescription();
    }
}