package com.yunqu.park.iot.model.dto.command;

import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 设置低电报警请求
 * 
 * <AUTHOR>
 * @since 2025-01-06
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class SetLowBatteryAlarmRequest extends BaseCommandReq {
    
    /**
     * 是否启用低电报警
     * true: 启用, false: 禁用
     */
    @NotNull(message = "低电报警开关不能为空")
    private Boolean enabled;
    
    /**
     * 低电报警阈值 (百分比: 0-100)
     */
    private Integer threshold;
}