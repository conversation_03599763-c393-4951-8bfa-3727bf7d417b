package com.yunqu.park.iot.service.command.impl;

import com.yunqu.park.iot.model.command.CommandType;
import com.yunqu.park.iot.model.command.dto.SetGprsSwitchParams;
import com.yunqu.park.iot.service.command.ICommand;
import lombok.RequiredArgsConstructor;

@RequiredArgsConstructor
public class SetGprsSwitchCommand implements ICommand {

    private final SetGprsSwitchParams params;
    private static final String TEMPLATE = "GPRSON,%d#";

    @Override
    public CommandType getType() {
        return CommandType.SET_GPRS_SWITCH;
    }

    @Override
    public String build() {
        return String.format(TEMPLATE, params.getState());
    }

    @Override
    public String getDescription() {
        return String.format("%s: %s", getType().getDescription(), params.getState() == 1 ? "开启" : "关闭");
    }
}