package com.yunqu.park.iot.controller;

import com.yunqu.park.iot.netty.codec.GT06ProtocolDecoder;
import com.yunqu.park.iot.netty.manager.DeviceConnectionManager;
import com.yunqu.park.iot.utils.ByteBufUtils;
import io.netty.buffer.ByteBuf;
import io.netty.buffer.Unpooled;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.*;
import java.util.concurrent.ConcurrentLinkedQueue;
import java.util.concurrent.atomic.AtomicLong;

/**
 * 协议调试控制器
 * 用于监控和测试GT06协议解码过程
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/iot/debug")
@Slf4j
public class ProtocolDebugController {

    @Autowired
    private DeviceConnectionManager connectionManager;

    // 解码统计信息
    private static final AtomicLong totalDecodeAttempts = new AtomicLong(0);
    private static final AtomicLong successfulDecodes = new AtomicLong(0);
    private static final AtomicLong failedDecodes = new AtomicLong(0);
    
    // 最近的数据包记录（最多保存100个）
    private static final Queue<PacketDebugInfo> recentPackets = new ConcurrentLinkedQueue<>();
    private static final int MAX_RECENT_PACKETS = 100;

    /**
     * 获取解码统计信息
     */
    @GetMapping("/decode/statistics")
    public Map<String, Object> getDecodeStatistics() {
        Map<String, Object> stats = new HashMap<>();
        
        long total = totalDecodeAttempts.get();
        long success = successfulDecodes.get();
        long failed = failedDecodes.get();
        
        stats.put("total_connections", connectionManager.getOnlineDeviceCount());
        stats.put("total_decode_attempts", total);
        stats.put("successful_decodes", success);
        stats.put("failed_decodes", failed);
        stats.put("decode_success_rate", total > 0 ? String.format("%.2f%%", (success * 100.0 / total)) : "0.00%");
        stats.put("recent_packets_count", recentPackets.size());
        
        return stats;
    }

    /**
     * 测试数据包解码
     * 用于验证特定hex数据的解码过程
     */
    @GetMapping("/decode/test")
    public Map<String, Object> testDecode(@RequestParam String hexData) {
        Map<String, Object> result = new HashMap<>();
        
        try {
            log.info("[DEBUG-TEST] 测试解码数据包: {}", hexData);
            
            // 将hex字符串转换为字节数组
            byte[] data = hexStringToBytes(hexData);
            ByteBuf buffer = Unpooled.wrappedBuffer(data);
            
            GT06ProtocolDecoder decoder = new GT06ProtocolDecoder();
            List<Object> out = new ArrayList<>();
            
            // 记录解码尝试
            totalDecodeAttempts.incrementAndGet();
            
            // 模拟解码过程
            decoder.decode(null, buffer, out);
            
            if (!out.isEmpty()) {
                successfulDecodes.incrementAndGet();
                result.put("success", true);
                result.put("decoded_messages", out.size());
                result.put("message", out.get(0).toString());
                log.info("[DEBUG-TEST] ✅ 解码成功: 消息数={}", out.size());
            } else {
                failedDecodes.incrementAndGet();
                result.put("success", false);
                result.put("error", "解码失败，未生成消息对象");
                log.warn("[DEBUG-TEST] ❌ 解码失败: 未生成消息对象");
            }
            
            result.put("input_hex", hexData);
            result.put("input_length", data.length);
            
        } catch (Exception e) {
            failedDecodes.incrementAndGet();
            result.put("success", false);
            result.put("error", e.getMessage());
            result.put("input_hex", hexData);
            log.error("[DEBUG-TEST] ❌ 解码异常: {}", e.getMessage(), e);
        }
        
        return result;
    }

    /**
     * 获取最近的原始数据包
     */
    @GetMapping("/packets/recent")
    public List<PacketDebugInfo> getRecentPackets() {
        return new ArrayList<>(recentPackets);
    }

    /**
     * 清除统计信息
     */
    @PostMapping("/statistics/clear")
    public Map<String, Object> clearStatistics() {
        totalDecodeAttempts.set(0);
        successfulDecodes.set(0);
        failedDecodes.set(0);
        recentPackets.clear();
        
        Map<String, Object> result = new HashMap<>();
        result.put("success", true);
        result.put("message", "统计信息已清除");
        
        log.info("[DEBUG-CLEAR] 解码统计信息已清除");
        return result;
    }

    /**
     * 获取连接状态信息
     */
    @GetMapping("/connections/status")
    public Map<String, Object> getConnectionStatus() {
        Map<String, Object> status = new HashMap<>();
        
        status.put("online_devices", connectionManager.getOnlineDeviceCount());
        status.put("connection_details", connectionManager.getConnectionStatistics());
        
        return status;
    }

    /**
     * 记录数据包信息（供解码器调用）
     */
    public static void recordPacket(String remoteAddress, byte[] data, boolean success, String error) {
        PacketDebugInfo info = new PacketDebugInfo();
        info.setTimestamp(System.currentTimeMillis());
        info.setRemoteAddress(remoteAddress);
        info.setHexData(ByteBufUtils.bytesToHexString(data));
        info.setLength(data.length);
        info.setSuccess(success);
        info.setError(error);
        
        // 添加到队列，保持最大数量限制
        recentPackets.offer(info);
        while (recentPackets.size() > MAX_RECENT_PACKETS) {
            recentPackets.poll();
        }
    }

    /**
     * 更新解码统计
     */
    public static void updateDecodeStatistics(boolean success) {
        totalDecodeAttempts.incrementAndGet();
        if (success) {
            successfulDecodes.incrementAndGet();
        } else {
            failedDecodes.incrementAndGet();
        }
    }

    /**
     * 将hex字符串转换为字节数组
     */
    private byte[] hexStringToBytes(String hexString) {
        // 移除空格和其他非hex字符
        hexString = hexString.replaceAll("[^0-9A-Fa-f]", "");
        
        if (hexString.length() % 2 != 0) {
            throw new IllegalArgumentException("Hex字符串长度必须是偶数");
        }
        
        byte[] bytes = new byte[hexString.length() / 2];
        for (int i = 0; i < bytes.length; i++) {
            int index = i * 2;
            bytes[i] = (byte) Integer.parseInt(hexString.substring(index, index + 2), 16);
        }
        
        return bytes;
    }

    /**
     * 数据包调试信息
     */
    public static class PacketDebugInfo {
        private long timestamp;
        private String remoteAddress;
        private String hexData;
        private int length;
        private boolean success;
        private String error;

        // Getters and Setters
        public long getTimestamp() { return timestamp; }
        public void setTimestamp(long timestamp) { this.timestamp = timestamp; }
        
        public String getRemoteAddress() { return remoteAddress; }
        public void setRemoteAddress(String remoteAddress) { this.remoteAddress = remoteAddress; }
        
        public String getHexData() { return hexData; }
        public void setHexData(String hexData) { this.hexData = hexData; }
        
        public int getLength() { return length; }
        public void setLength(int length) { this.length = length; }
        
        public boolean isSuccess() { return success; }
        public void setSuccess(boolean success) { this.success = success; }
        
        public String getError() { return error; }
        public void setError(String error) { this.error = error; }
        
        public String getFormattedTimestamp() {
            return new Date(timestamp).toString();
        }
    }
}
