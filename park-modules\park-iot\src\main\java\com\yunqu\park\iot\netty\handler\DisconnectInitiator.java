package com.yunqu.park.iot.netty.handler;

/**
 * 连接断开发起方枚举
 *
 * <AUTHOR>
 */
public enum DisconnectInitiator {
    /**
     * 客户端主动断开
     */
    CLIENT("客户端"),
    
    /**
     * 服务端主动断开
     */
    SERVER("服务端");
    
    private final String description;
    
    DisconnectInitiator(String description) {
        this.description = description;
    }
    
    public String getDescription() {
        return description;
    }
}