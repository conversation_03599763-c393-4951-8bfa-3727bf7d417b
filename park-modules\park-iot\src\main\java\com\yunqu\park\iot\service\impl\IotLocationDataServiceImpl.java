package com.yunqu.park.iot.service.impl;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yunqu.park.common.core.utils.MapstructUtils;
import com.yunqu.park.common.mybatis.core.page.PageQuery;
import com.yunqu.park.common.mybatis.core.page.TableDataInfo;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yunqu.park.common.redis.utils.RedisUtils;
import com.yunqu.park.iot.constant.IotConstants;
import com.yunqu.park.iot.domain.IotLocationData;
import com.yunqu.park.iot.domain.bo.IotLocationDataBo;
import com.yunqu.park.iot.domain.vo.IotLocationDataVo;
import com.yunqu.park.iot.mapper.IotLocationDataMapper;
import com.yunqu.park.iot.service.IIotLocationDataService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.Duration;
import java.util.Collection;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 设备定位数据Service业务层处理
 *
 * <AUTHOR>
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class IotLocationDataServiceImpl extends ServiceImpl<IotLocationDataMapper, IotLocationData> implements IIotLocationDataService {

    private final IotLocationDataMapper baseMapper;

    /**
     * 查询设备定位数据
     */
    @Override
    public IotLocationDataVo queryById(Long locationId) {
        IotLocationData entity = baseMapper.selectById(locationId);
        return entity != null ? MapstructUtils.convert(entity, IotLocationDataVo.class) : null;
    }

    /**
     * 查询设备定位数据列表
     */
    @Override
    public TableDataInfo<IotLocationDataVo> queryPageList(IotLocationDataBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<IotLocationData> lqw = buildQueryWrapper(bo);
        Page<IotLocationDataVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询设备定位数据列表
     */
    @Override
    public List<IotLocationDataVo> queryList(IotLocationDataBo bo) {
        LambdaQueryWrapper<IotLocationData> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<IotLocationData> buildQueryWrapper(IotLocationDataBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<IotLocationData> lqw = Wrappers.<IotLocationData>lambdaQuery();
        lqw.eq(bo.getDeviceId() != null, IotLocationData::getDeviceId, bo.getDeviceId());
        lqw.eq(StrUtil.isNotBlank(bo.getImei()), IotLocationData::getImei, bo.getImei());
        lqw.eq(StrUtil.isNotBlank(bo.getGpsStatus()), IotLocationData::getGpsStatus, bo.getGpsStatus());
        lqw.eq(StrUtil.isNotBlank(bo.getAccStatus()), IotLocationData::getAccStatus, bo.getAccStatus());
        lqw.between(params.get("beginGpsTime") != null && params.get("endGpsTime") != null,
                IotLocationData::getGpsTime, params.get("beginGpsTime"), params.get("endGpsTime"));
        lqw.orderByDesc(IotLocationData::getGpsTime);
        return lqw;
    }

    /**
     * 新增设备定位数据
     */
    @Override
    public Boolean insertByBo(IotLocationDataBo bo) {
        IotLocationData add = MapstructUtils.convert(bo, IotLocationData.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setLocationId(add.getLocationId());
        }
        return flag;
    }

    /**
     * 修改设备定位数据
     */
    @Override
    public Boolean updateByBo(IotLocationDataBo bo) {
        IotLocationData update = MapstructUtils.convert(bo, IotLocationData.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(IotLocationData entity) {
        // 可以添加业务校验逻辑
    }

    /**
     * 批量删除设备定位数据
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            // 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteBatchIds(ids) > 0;
    }

    @Override
    public IotLocationDataVo getCurrentLocation(String imei) {
        try {
            // 先从缓存获取
            String cacheKey = IotConstants.CacheKeys.LATEST_LOCATION_PREFIX + imei;
            IotLocationDataVo cachedLocation = RedisUtils.getCacheObject(cacheKey);
            if (cachedLocation != null) {
                return cachedLocation;
            }

            // 从数据库获取最新位置
            IotLocationDataVo location = baseMapper.selectLatestByImei(imei);
            if (location != null) {
                // 缓存5分钟
                RedisUtils.setCacheObject(cacheKey, location, Duration.ofMinutes(5));
            }
            return location;
        } catch (Exception e) {
            log.error("Failed to get current location for device {}: {}", imei, e.getMessage(), e);
            return null;
        }
    }

    @Override
    public List<IotLocationDataVo> getTrackData(String imei, Date startTime, Date endTime) {
        try {
            return baseMapper.selectTrackData(imei, startTime, endTime);
        } catch (Exception e) {
            log.error("Failed to get track data for device {}: {}", imei, e.getMessage(), e);
            return List.of();
        }
    }

    @Override
    public List<IotLocationDataVo> getPlaybackData(String imei, Date startTime, Date endTime, Integer interval) {
        try {
            if (interval == null || interval <= 0) {
                interval = 60; // 默认60秒间隔
            }
            return baseMapper.selectPlaybackData(imei, startTime, endTime, interval);
        } catch (Exception e) {
            log.error("Failed to get playback data for device {}: {}", imei, e.getMessage(), e);
            return List.of();
        }
    }

    @Override
    public int batchSaveLocationData(List<IotLocationData> locationDataList) {
        try {
            if (locationDataList == null || locationDataList.isEmpty()) {
                return 0;
            }
            return baseMapper.batchInsert(locationDataList);
        } catch (Exception e) {
            log.error("Failed to batch save location data: {}", e.getMessage(), e);
            return 0;
        }
    }

    @Override
    public Boolean saveLocationData(IotLocationData locationData) {
        try {
            boolean result = baseMapper.insert(locationData) > 0;
            
            if (result) {
                // 更新缓存中的最新位置
                String cacheKey = IotConstants.CacheKeys.LATEST_LOCATION_PREFIX + locationData.getImei();
                IotLocationDataVo locationVo = MapstructUtils.convert(locationData, IotLocationDataVo.class);
                RedisUtils.setCacheObject(cacheKey, locationVo, Duration.ofMinutes(5));
                
                log.debug("Saved location data for device: {}", locationData.getImei());
            }
            
            return result;
        } catch (Exception e) {
            log.error("Failed to save location data for device {}: {}", locationData.getImei(), e.getMessage(), e);
            return false;
        }
    }
}
