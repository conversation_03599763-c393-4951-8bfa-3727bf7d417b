package com.yunqu.park.iot.test;

import com.yunqu.park.common.core.domain.R;
import com.yunqu.park.iot.utils.IotLogUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 日志测试控制器
 * 用于测试IoT模块的日志配置是否生效
 *
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/iot/test/log")
public class LogTestController {

    /**
     * 测试各种类型的日志输出
     */
    @GetMapping("/test-all")
    public R<String> testAllLogs() {
        log.info("=== IoT日志测试开始 ===");
        
        // 测试连接日志
        log.info("[CONNECTION-REGISTER] 🔗 Test connection log: IMEI=TEST123456789, ChannelId=test-channel-001");
        log.info("[DEVICE-ONLINE] ✅ Test device online log: IMEI=TEST123456789, ClientIP=*************");
        
        // 测试协议日志
        log.info("[PROTOCOL-LOGIN] 🔐 Test protocol log: IMEI=TEST123456789, Protocol=0x01");
        log.info("[MESSAGE-RECEIVED] 📨 Test message log: IMEI=TEST123456789, Protocol=0x12, SequenceNumber=1");
        
        // 测试指令日志
        log.info("[COMMAND-RESTART] 🔄 Test command log: IMEI=TEST123456789, Command=RESET#");
        log.info("[COMMAND-SEND] ✅ Test command send log: IMEI=TEST123456789, Success=true");
        
        // 测试服务器日志
        log.info("[TCP-SERVER] 🚀 Test server log: Port=8888, Status=RUNNING");
        
        // 测试错误日志
        log.error("[PROTOCOL-ERROR] ❌ Test error log: IMEI=TEST123456789, Error=CRC_VALIDATION_FAILED");
        
        // 测试调试日志
        log.debug("[DEBUG] Test debug log: This should only appear in debug mode");
        
        // 使用工具类测试
        IotLogUtils.logDeviceConnection("TEST123456789", "*************:12345", "test-channel-001");
        IotLogUtils.logMessageReceived("TEST123456789", "0x12", 1, 20);
        IotLogUtils.logCommandSent("TEST123456789", "RESTART", "RESET#", true);
        
        log.info("=== IoT日志测试完成 ===");
        
        return R.ok("日志测试完成，请检查日志文件：logs/iot/");
    }

    /**
     * 测试连接日志
     */
    @GetMapping("/test-connection")
    public R<String> testConnectionLogs() {
        log.info("[CONNECTION-REGISTER] 🔗 Device connection test: IMEI=CONN123456789, RemoteAddress=*************:54321, ChannelId=conn-test-001");
        log.info("[DEVICE-ONLINE] ✅ Device online test: IMEI=CONN123456789, ClientIP=*************");
        log.info("[DEVICE-OFFLINE] 🔌 Device offline test: IMEI=CONN123456789");
        log.info("[CONNECTION-REMOVE] ✅ Connection removed test: IMEI=CONN123456789");
        
        return R.ok("连接日志测试完成");
    }

    /**
     * 测试协议日志
     */
    @GetMapping("/test-protocol")
    public R<String> testProtocolLogs() {
        log.info("[PROTOCOL-LOGIN] 🔐 Login protocol test: IMEI=PROT123456789, ClientIP=192.168.1.300");
        log.info("[PROTOCOL-HEARTBEAT] 💓 Heartbeat test: IMEI=PROT123456789, SequenceNumber=100");
        log.info("[PROTOCOL-LOCATION] 📍 Location data test: IMEI=PROT123456789, Lat=39.9042, Lng=116.4074");
        log.info("[MESSAGE-RECEIVED] 📨 Message received test: Protocol=0x13, ContentLength=25");
        
        return R.ok("协议日志测试完成");
    }

    /**
     * 测试指令日志
     */
    @GetMapping("/test-command")
    public R<String> testCommandLogs() {
        log.info("[COMMAND-RESTART] 🔄 Restart command test: IMEI=CMD123456789");
        log.info("[COMMAND-SEND] ✅ Command send success test: IMEI=CMD123456789, Command=RESET#");
        log.error("[COMMAND-SEND] ❌ Command send failed test: IMEI=CMD123456789, Error=DEVICE_OFFLINE");
        
        return R.ok("指令日志测试完成");
    }

    /**
     * 测试服务器日志
     */
    @GetMapping("/test-server")
    public R<String> testServerLogs() {
        log.info("[TCP-SERVER] 🚀 Server startup test: Port=8888, BossThreads=1, WorkerThreads=8");
        log.info("[TCP-SERVER] ✅ Server running test: ActiveConnections=5");
        log.warn("[TCP-SERVER] ⚠️ Server warning test: HighConnectionCount=1000");
        
        return R.ok("服务器日志测试完成");
    }

    /**
     * 获取统计信息
     */
    @GetMapping("/statistics")
    public R<String> getStatistics() {
        String stats = IotLogUtils.getStatistics();
        log.info("当前统计信息: {}", stats);
        return R.ok(stats);
    }

    /**
     * 重置统计信息
     */
    @GetMapping("/reset-statistics")
    public R<String> resetStatistics() {
        IotLogUtils.resetStatistics();
        log.info("统计信息已重置");
        return R.ok("统计信息已重置");
    }
}
