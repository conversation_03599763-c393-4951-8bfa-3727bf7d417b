package com.yunqu.park.iot.model.command.dto;

import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = false)
public class SetLanguageParams extends CommandParams {
    @NotNull(message = "语言代码不能为空")
    @Min(value = 0, message = "语言代码必须为0或1")
    @Max(value = 1, message = "语言代码必须为0或1")
    private Integer langCode;
}