<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yunqu.park.iot.mapper.IotAlarmRecordMapper">

    <resultMap type="com.yunqu.park.iot.domain.IotAlarmRecord" id="IotAlarmRecordResult">
        <result property="alarmId"    column="alarm_id"    />
        <result property="deviceId"    column="device_id"    />
        <result property="imei"    column="imei"    />
        <result property="alarmType"    column="alarm_type"    />
        <result property="alarmName"    column="alarm_name"    />
        <result property="alarmTime"    column="alarm_time"    />
        <result property="latitude"    column="latitude"    />
        <result property="longitude"    column="longitude"    />
        <result property="address"    column="address"    />
        <result property="alarmStatus"    column="alarm_status"    />
        <result property="handleTime"    column="handle_time"    />
        <result property="handleUser"    column="handle_user"    />
        <result property="handleRemark"    column="handle_remark"    />
        <result property="tenantId"    column="tenant_id"    />
        <result property="createDept"    column="create_dept"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <resultMap type="com.yunqu.park.iot.domain.vo.IotAlarmRecordVo" id="IotAlarmRecordVoResult">
        <result property="alarmId"    column="alarm_id"    />
        <result property="deviceId"    column="device_id"    />
        <result property="imei"    column="imei"    />
        <result property="alarmType"    column="alarm_type"    />
        <result property="alarmName"    column="alarm_name"    />
        <result property="alarmTime"    column="alarm_time"    />
        <result property="latitude"    column="latitude"    />
        <result property="longitude"    column="longitude"    />
        <result property="address"    column="address"    />
        <result property="alarmStatus"    column="alarm_status"    />
        <result property="handleTime"    column="handle_time"    />
        <result property="handleUser"    column="handle_user"    />
        <result property="handleRemark"    column="handle_remark"    />
        <result property="tenantId"    column="tenant_id"    />
        <result property="createDept"    column="create_dept"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectIotAlarmRecordVo">
        select alarm_id, device_id, imei, alarm_type, alarm_name, alarm_time, latitude, longitude, address, alarm_status, handle_time, handle_user, handle_remark, tenant_id, create_dept, create_by, create_time, update_by, update_time from iot_alarm_record
    </sql>

    <select id="selectUnhandledAlarmsByImei" parameterType="String" resultMap="IotAlarmRecordVoResult">
        <include refid="selectIotAlarmRecordVo"/>
        where imei = #{imei} and alarm_status = '0'
        order by alarm_time desc
    </select>

    <update id="updateAlarmHandleStatus">
        update iot_alarm_record 
        set alarm_status = '1',
            handle_time = now(),
            handle_user = #{handleUser},
            handle_remark = #{handleRemark},
            update_time = now()
        where alarm_id = #{alarmId}
    </update>

    <select id="selectAlarmStatistics" resultType="java.util.Map">
        select 
            count(*) as total_count,
            sum(case when alarm_status = '0' then 1 else 0 end) as unhandled_count,
            sum(case when alarm_status = '1' then 1 else 0 end) as handled_count,
            alarm_type,
            count(*) as type_count
        from iot_alarm_record 
        where 1=1
        <if test="startTime != null">
            and alarm_time >= #{startTime}
        </if>
        <if test="endTime != null">
            and alarm_time &lt;= #{endTime}
        </if>
        group by alarm_type
        order by type_count desc
    </select>

    <select id="selectAlarmsByDeviceAndTime" resultMap="IotAlarmRecordVoResult">
        <include refid="selectIotAlarmRecordVo"/>
        where device_id = #{deviceId}
        <if test="startTime != null">
            and alarm_time >= #{startTime}
        </if>
        <if test="endTime != null">
            and alarm_time &lt;= #{endTime}
        </if>
        order by alarm_time desc
    </select>

</mapper>
