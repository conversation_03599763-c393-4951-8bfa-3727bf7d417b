package com.yunqu.park.iot.service.command.impl;

import com.yunqu.park.iot.model.command.CommandType;
import com.yunqu.park.iot.model.command.dto.SetLanguageParams;
import com.yunqu.park.iot.service.command.ICommand;
import lombok.RequiredArgsConstructor;

@RequiredArgsConstructor
public class Set<PERSON>anguageCommand implements ICommand {

    private final SetLanguageParams params;
    private static final String TEMPLATE = "LANG,%d#";

    @Override
    public CommandType getType() {
        return CommandType.SET_LANGUAGE;
    }

    @Override
    public String build() {
        return String.format(TEMPLATE, params.getLangCode());
    }

    @Override
    public String getDescription() {
        return String.format("%s: %s",
            getType().getDescription(), params.getLangCode() == 1 ? "中文" : "英文");
    }
}