package com.yunqu.park.iot.model.dto.command;

import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.Max;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 设置GPS休眠工作请求
 * 
 * <AUTHOR>
 * @since 2025-01-06
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class SetGpsSleepWorkRequest extends BaseCommandReq {
    
    /**
     * GPS休眠时间 (分钟: 1-1440)
     */
    @NotNull(message = "GPS休眠时间不能为空")
    @Min(value = 1, message = "GPS休眠时间不能小于1分钟")
    @Max(value = 1440, message = "GPS休眠时间不能大于1440分钟")
    private Integer sleepTime;
    
    /**
     * GPS工作时间 (分钟: 1-1440)
     */
    @NotNull(message = "GPS工作时间不能为空")
    @Min(value = 1, message = "GPS工作时间不能小于1分钟")
    @Max(value = 1440, message = "GPS工作时间不能大于1440分钟")
    private Integer workTime;
}