package com.yunqu.park.iot.config;

import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;

/**
 * IoT模块日志配置管理
 * 负责模块日志的初始化和管理
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class IotLogConfig {

    /**
     * 应用启动完成后的日志配置检查
     */
    @EventListener(ApplicationReadyEvent.class)
    public void onApplicationReady() {
        checkLogConfiguration();
        printLogConfigurationInfo();
    }

    /**
     * 检查日志配置是否正确
     */
    private void checkLogConfiguration() {
        try {
            // 检查日志目录是否可创建
            java.nio.file.Path logPath = java.nio.file.Paths.get("logs/iot");
            if (!java.nio.file.Files.exists(logPath)) {
                java.nio.file.Files.createDirectories(logPath);
                log.info("Created IoT log directory: {}", logPath.toAbsolutePath());
            }

            // 检查日志配置是否生效
            ch.qos.logback.classic.Logger iotLogger = 
                (ch.qos.logback.classic.Logger) org.slf4j.LoggerFactory.getLogger("com.yunqu.park.iot");
            
            boolean hasIotAppenders = iotLogger.iteratorForAppenders().hasNext();
            if (hasIotAppenders) {
                log.info("IoT module log configuration is active");
            } else {
                log.warn("IoT module log configuration may not be properly loaded");
            }

        } catch (Exception e) {
            log.error("Failed to check IoT log configuration: {}", e.getMessage(), e);
        }
    }

    /**
     * 打印日志配置信息
     */
    private void printLogConfigurationInfo() {
        log.info("=== IoT Module Log Configuration ===");
        log.info("Log files location: logs/iot/");
        log.info("Available log files:");
        log.info("  - connection.log    : Device connection logs");
        log.info("  - protocol.log      : Protocol processing logs");
        log.info("  - command.log       : Command processing logs");
        log.info("  - server.log        : TCP server logs");
        log.info("  - iot-all.log       : All IoT module logs");
        log.info("Log configuration source: logback-iot-config.xml");
        log.info("=====================================");
    }

    /**
     * 获取日志配置状态
     */
    public LogConfigStatus getLogConfigStatus() {
        LogConfigStatus status = new LogConfigStatus();
        
        try {
            // 检查日志目录
            java.nio.file.Path logPath = java.nio.file.Paths.get("logs/iot");
            status.setLogDirectoryExists(java.nio.file.Files.exists(logPath));
            status.setLogDirectoryPath(logPath.toAbsolutePath().toString());

            // 检查Logger配置
            ch.qos.logback.classic.Logger iotLogger = 
                (ch.qos.logback.classic.Logger) org.slf4j.LoggerFactory.getLogger("com.yunqu.park.iot");
            status.setLoggerConfigured(iotLogger.iteratorForAppenders().hasNext());
            status.setLogLevel(iotLogger.getLevel() != null ? iotLogger.getLevel().toString() : "INHERITED");

            // 统计Appender数量
            int appenderCount = 0;
            while (iotLogger.iteratorForAppenders().hasNext()) {
                iotLogger.iteratorForAppenders().next();
                appenderCount++;
            }
            status.setAppenderCount(appenderCount);

        } catch (Exception e) {
            log.error("Failed to get log config status: {}", e.getMessage(), e);
            status.setError(e.getMessage());
        }

        return status;
    }

    /**
     * 日志配置状态信息
     */
    public static class LogConfigStatus {
        private boolean logDirectoryExists;
        private String logDirectoryPath;
        private boolean loggerConfigured;
        private String logLevel;
        private int appenderCount;
        private String error;

        // Getters and Setters
        public boolean isLogDirectoryExists() { return logDirectoryExists; }
        public void setLogDirectoryExists(boolean logDirectoryExists) { this.logDirectoryExists = logDirectoryExists; }

        public String getLogDirectoryPath() { return logDirectoryPath; }
        public void setLogDirectoryPath(String logDirectoryPath) { this.logDirectoryPath = logDirectoryPath; }

        public boolean isLoggerConfigured() { return loggerConfigured; }
        public void setLoggerConfigured(boolean loggerConfigured) { this.loggerConfigured = loggerConfigured; }

        public String getLogLevel() { return logLevel; }
        public void setLogLevel(String logLevel) { this.logLevel = logLevel; }

        public int getAppenderCount() { return appenderCount; }
        public void setAppenderCount(int appenderCount) { this.appenderCount = appenderCount; }

        public String getError() { return error; }
        public void setError(String error) { this.error = error; }

        @Override
        public String toString() {
            return String.format(
                "LogConfigStatus{logDirectoryExists=%s, logDirectoryPath='%s', loggerConfigured=%s, logLevel='%s', appenderCount=%d, error='%s'}",
                logDirectoryExists, logDirectoryPath, loggerConfigured, logLevel, appenderCount, error
            );
        }
    }
}
