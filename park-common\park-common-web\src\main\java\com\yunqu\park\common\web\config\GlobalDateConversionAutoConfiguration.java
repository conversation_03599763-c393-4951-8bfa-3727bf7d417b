package com.yunqu.park.common.web.config;

import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.AutoConfiguration;
import org.springframework.boot.autoconfigure.condition.ConditionalOnWebApplication;
import org.springframework.context.annotation.Import;

/**
 * 全局日期转换自动配置类
 * 确保全局日期转换配置在Web应用中自动生效
 *
 * <AUTHOR>
 */
@Slf4j
@AutoConfiguration
@ConditionalOnWebApplication
@Import({
    GlobalDateConverterConfig.class,
    GlobalWebMvcConfig.class,
    GlobalJacksonConfig.class
})
public class GlobalDateConversionAutoConfiguration {

    public GlobalDateConversionAutoConfiguration() {
        log.info("Global date conversion auto configuration initialized");
    }
}
