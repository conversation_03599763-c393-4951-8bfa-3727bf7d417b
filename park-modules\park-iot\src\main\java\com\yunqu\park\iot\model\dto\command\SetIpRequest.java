package com.yunqu.park.iot.model.dto.command;

import com.yunqu.park.iot.model.command.dto.SetIpParams;
import lombok.Data;
import lombok.EqualsAndHashCode;

import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;

/**
 * 设置IP指令请求
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class SetIpRequest extends BaseCommandReq {

    /**
     * IP设置参数
     */
    @Valid
    @NotNull(message = "IP参数不能为空")
    private SetIpParams params;
}