package com.yunqu.park.iot.model.dto.command;

import com.yunqu.park.iot.model.command.dto.SetAngleValueParams;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 设置角度值指令请求
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "设置角度值指令请求")
public class SetAngleValueRequest extends BaseCommandReq {

    @Schema(description = "角度值参数")
    private SetAngleValueParams params;
}