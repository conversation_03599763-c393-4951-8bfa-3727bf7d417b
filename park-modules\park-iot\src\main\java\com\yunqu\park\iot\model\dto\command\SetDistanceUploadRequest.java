package com.yunqu.park.iot.model.dto.command;

import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.Max;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 设置距离上传请求
 * 
 * <AUTHOR>
 * @since 2025-01-06
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class SetDistanceUploadRequest extends BaseCommandReq {
    
    /**
     * 距离上传间隔 (米: 1-65535)
     */
    @NotNull(message = "距离上传间隔不能为空")
    @Min(value = 1, message = "距离上传间隔不能小于1米")
    @Max(value = 65535, message = "距离上传间隔不能大于65535米")
    private Integer distance;
}