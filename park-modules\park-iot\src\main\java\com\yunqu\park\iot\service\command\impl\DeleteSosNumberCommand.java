package com.yunqu.park.iot.service.command.impl;

import com.yunqu.park.iot.model.command.CommandType;
import com.yunqu.park.iot.model.command.dto.DeleteSosNumberParams;
import com.yunqu.park.iot.service.command.ICommand;
import lombok.RequiredArgsConstructor;

@RequiredArgsConstructor
public class DeleteSosNumberCommand implements ICommand {

    private final DeleteSosNumberParams params;
    private static final String TEMPLATE = "SOS,D,%s#";

    @Override
    public CommandType getType() {
        return CommandType.DELETE_SOS_NUMBER;
    }

    @Override
    public String build() {
        return String.format(TEMPLATE, params.getPhone());
    }

    @Override
    public String getDescription() {
        return String.format("%s: %s", getType().getDescription(), params.getPhone());
    }
}