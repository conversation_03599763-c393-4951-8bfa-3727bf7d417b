package com.yunqu.park.iot.service.command.impl;

import com.yunqu.park.iot.model.command.CommandType;
import com.yunqu.park.iot.model.command.dto.SetSpeedAlarmParams;
import com.yunqu.park.iot.service.command.ICommand;
import lombok.RequiredArgsConstructor;

/**
 * 设置超速报警指令实现
 *
 * <p>实现车辆超速报警功能的配置，当车辆速度超过设定阈值时触发报警。
 * 该指令支持灵活的报警模式配置，可以根据不同场景设置不同的报警策略。</p>
 *
 * <h3>指令参数：</h3>
 * <ul>
 *   <li><strong>state</strong>：报警状态（ON=启用，OFF=禁用）</li>
 *   <li><strong>speed</strong>：超速阈值（单位：km/h，范围：0-255）</li>
 *   <li><strong>mode</strong>：报警模式（1=仅上报，2=声光报警，3=断油电）</li>
 * </ul>
 *
 * <h3>报警模式说明：</h3>
 * <ul>
 *   <li><strong>模式1（仅上报）</strong>：超速时只向服务器发送报警信息</li>
 *   <li><strong>模式2（声光报警）</strong>：超速时设备发出声音和灯光警告</li>
 *   <li><strong>模式3（断油电）</strong>：超速时切断车辆油路或电路（谨慎使用）</li>
 * </ul>
 *
 * <h3>使用场景：</h3>
 * <ul>
 *   <li>车队管理中的安全驾驶监控</li>
 *   <li>物流运输的速度合规检查</li>
 *   <li>特殊区域（学校、医院）的限速管理</li>
 *   <li>驾驶行为分析和安全评估</li>
 * </ul>
 *
 * <h3>配置建议：</h3>
 * <ul>
 *   <li><strong>城市道路</strong>：建议设置60-80 km/h</li>
 *   <li><strong>高速公路</strong>：建议设置120-140 km/h</li>
 *   <li><strong>特殊区域</strong>：建议设置30-50 km/h</li>
 *   <li><strong>断油电模式</strong>：仅在紧急情况下使用，注意安全</li>
 * </ul>
 *
 * <h3>注意事项：</h3>
 * <ul>
 *   <li>速度检测基于GPS数据，可能存在1-2秒延迟</li>
 *   <li>断油电功能需要硬件支持，使用前确认设备型号</li>
 *   <li>频繁的超速报警可能影响驾驶体验，合理设置阈值</li>
 *   <li>建议结合地理围栏功能实现区域性限速</li>
 * </ul>
 *
 * <h3>协议格式：</h3>
 * <pre>{@code
 * 发送: "SPEED,ON,80,2#"
 * 参数: state=ON, speed=80, mode=2
 * 含义: 启用超速报警，阈值80km/h，声光报警模式
 * }</pre>
 *
 * <AUTHOR>
 * @version 2.0
 * @since 2025-01-06
 * @see SetSpeedAlarmParams
 * @see com.yunqu.park.iot.model.command.CommandType#SET_SPEED_ALARM
 */
@RequiredArgsConstructor
public class SetSpeedAlarmCommand implements ICommand {

    /** 指令参数对象 */
    private final SetSpeedAlarmParams params;

    /** 超速报警指令模板：SPEED,状态,速度,模式# */
    private static final String TEMPLATE = "SPEED,%s,%d,%d#";

    @Override
    public CommandType getType() {
        return CommandType.SET_SPEED_ALARM;
    }

    @Override
    public String build() {
        return String.format(TEMPLATE, params.getState(), params.getSpeed(), params.getMode());
    }

    @Override
    public String getDescription() {
        return String.format("%s: 状态=%s, 速度=%d km/h, 模式=%d",
            getType().getDescription(), params.getState(), params.getSpeed(), params.getMode());
    }
}