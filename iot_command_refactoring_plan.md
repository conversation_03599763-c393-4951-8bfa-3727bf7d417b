# IoT Command Controller 重构计划

**目标**: 为每个 IoT 指令创建一个独立的、类型安全的 API 端点，同时保持现有 `/send` 和 `/send-batch` 接口的向后兼容性。

**核心策略**:
1.  **创建特定指令的 DTO**: 为每个指令创建专门的 Request DTO，封装设备 ID 和指令所需的强类型参数。
2.  **扩展 Service 层**: 在 `IIotCommandService` 和 `IotCommandServiceImpl` 中为每个指令添加新的方法。
3.  **扩展 Controller 层**: 在 `IotCommandController` 中为每个指令添加新的 RESTful 端点。
4.  **复用现有逻辑**: 新的 Service 方法将构建一个通用的 `CommandRequest` 对象，并调用现有的 `sendCommand` 方法来复用底层的指令发送逻辑，避免代码重复。

**计划步骤**:

以 `REBOOT`（无参数指令）和 `SET_IP`（有参数指令）为例，演示重构模式。后续所有指令都将遵循此模式。

1.  **创建 Request DTO 包**:
    *   在 `park-modules/park-iot/src/main/java/com/yunqu/park/iot/model/dto/` 路径下创建一个新的包 `command`，用于存放所有与新接口相关的请求 DTO。

2.  **创建基类 Request DTO**:
    *   创建一个 `BaseCommandReq.java`，包含所有请求都需要的 `deviceId` 字段，以实现复用。

3.  **为具体指令创建 DTO**:
    *   **`RebootRequest` (无参数指令)**: 继承 `BaseCommandReq`。
    *   **`SetIpRequest` (有参数指令)**: 继承 `BaseCommandReq`，并包含 `SetIpParams`。

4.  **更新 Service 接口 (`IIotCommandService`)**:
    *   添加 `reboot(RebootRequest request)` 和 `setIp(SetIpRequest request)` 方法。

5.  **更新 Service 实现 (`IotCommandServiceImpl`)**:
    *   实现新接口，内部转换为对 `sendCommand` 的调用。

6.  **更新 Controller (`IotCommandController`)**:
    *   添加 `@PostMapping("/reboot")` 和 `@PostMapping("/set-ip")` 端点。