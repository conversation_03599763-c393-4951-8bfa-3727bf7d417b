package com.yunqu.park.iot.model.dto.command;

import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.Max;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 设置急刹车报警请求
 * 
 * <AUTHOR>
 * @since 2025-01-06
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class SetHarshBrakingAlarmRequest extends BaseCommandReq {
    
    /**
     * 是否启用急刹车报警
     * true: 启用, false: 禁用
     */
    @NotNull(message = "急刹车报警开关不能为空")
    private Boolean enabled;
    
    /**
     * 急刹车阈值 (m/s²: 1-10)
     */
    @Min(value = 1, message = "急刹车阈值不能小于1")
    @Max(value = 10, message = "急刹车阈值不能大于10")
    private Integer threshold;
}