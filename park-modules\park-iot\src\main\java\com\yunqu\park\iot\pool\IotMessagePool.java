package com.yunqu.park.iot.pool;

import com.yunqu.park.iot.netty.protocol.IotMessage;
import jakarta.annotation.PostConstruct;
import jakarta.annotation.PreDestroy;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.pool2.BasePooledObjectFactory;
import org.apache.commons.pool2.ObjectPool;
import org.apache.commons.pool2.PooledObject;
import org.apache.commons.pool2.impl.DefaultPooledObject;
import org.apache.commons.pool2.impl.GenericObjectPool;
import org.apache.commons.pool2.impl.GenericObjectPoolConfig;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;


/**
 * IoT消息对象池
 * 阶段2优化：减少对象创建开销，提升解析性能
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class IotMessagePool {

    @Value("${iot.performance.object-pool-enabled:true}")
    private boolean poolEnabled;

    @Value("${iot.performance.connection-pool-size:1000}")
    private int maxTotal;

    private ObjectPool<IotMessage> messagePool;

    @PostConstruct
    public void initPool() {
        if (!poolEnabled) {
            log.info("[MESSAGE-POOL] 对象池已禁用，将使用直接创建模式");
            return;
        }

        try {
            // 配置对象池
            GenericObjectPoolConfig<IotMessage> config = new GenericObjectPoolConfig<>();
            config.setMaxTotal(maxTotal);                    // 最大对象数
            config.setMaxIdle(maxTotal / 10);               // 最大空闲对象数
            config.setMinIdle(10);                          // 最小空闲对象数
            config.setTestOnBorrow(false);                  // 借用时不测试
            config.setTestOnReturn(false);                  // 归还时不测试
            config.setTestWhileIdle(true);                  // 空闲时测试
            config.setTimeBetweenEvictionRuns(java.time.Duration.ofSeconds(30)); // 清理间隔30秒
            config.setBlockWhenExhausted(false);            // 池耗尽时不阻塞

            // 创建对象池
            messagePool = new GenericObjectPool<>(new IotMessageFactory(), config);

            log.info("[MESSAGE-POOL] ✅ IoT消息对象池初始化成功: MaxTotal={}, MaxIdle={}, MinIdle={}",
                    config.getMaxTotal(), config.getMaxIdle(), config.getMinIdle());

        } catch (Exception e) {
            log.error("[MESSAGE-POOL] ❌ IoT消息对象池初始化失败: {}", e.getMessage(), e);
            // 初始化失败时禁用对象池
            poolEnabled = false;
        }
    }

    /**
     * 借用IoT消息对象
     * @return IoT消息对象
     */
    public IotMessage borrowMessage() {
        if (!poolEnabled || messagePool == null) {
            return new IotMessage();
        }

        try {
            IotMessage message = messagePool.borrowObject();
            if (message != null) {
                message.reset(); // 重置对象状态
                log.debug("[MESSAGE-POOL] 从对象池借用消息对象");
                return message;
            }
        } catch (Exception e) {
            log.warn("[MESSAGE-POOL] ⚠️ 从对象池借用对象失败，使用直接创建: {}", e.getMessage());
        }

        // 池借用失败时直接创建
        return new IotMessage();
    }

    /**
     * 归还IoT消息对象
     * @param message IoT消息对象
     */
    public void returnMessage(IotMessage message) {
        if (!poolEnabled || messagePool == null || message == null) {
            return;
        }

        try {
            message.reset(); // 重置对象状态
            messagePool.returnObject(message);
            log.debug("[MESSAGE-POOL] 消息对象已归还到对象池");
        } catch (Exception e) {
            log.warn("[MESSAGE-POOL] ⚠️ 归还对象到池失败: {}", e.getMessage());
            // 归还失败不影响业务逻辑
        }
    }

    /**
     * 获取对象池统计信息
     * @return 统计信息
     */
    public String getPoolStatistics() {
        if (!poolEnabled || messagePool == null) {
            return "对象池已禁用";
        }

        try {
            GenericObjectPool<IotMessage> pool = (GenericObjectPool<IotMessage>) messagePool;
            return String.format("Active: %d, Idle: %d, Created: %d, Borrowed: %d, Returned: %d",
                    pool.getNumActive(),
                    pool.getNumIdle(),
                    pool.getCreatedCount(),
                    pool.getBorrowedCount(),
                    pool.getReturnedCount());
        } catch (Exception e) {
            return "统计信息获取失败: " + e.getMessage();
        }
    }

    @PreDestroy
    public void destroyPool() {
        if (messagePool != null) {
            try {
                messagePool.close();
                log.info("[MESSAGE-POOL] IoT消息对象池已关闭");
            } catch (Exception e) {
                log.error("[MESSAGE-POOL] 关闭对象池时发生异常: {}", e.getMessage(), e);
            }
        }
    }

    /**
     * IoT消息对象工厂
     */
    private static class IotMessageFactory extends BasePooledObjectFactory<IotMessage> {

        @Override
        public IotMessage create() throws Exception {
            return new IotMessage();
        }

        @Override
        public PooledObject<IotMessage> wrap(IotMessage obj) {
            return new DefaultPooledObject<>(obj);
        }

        @Override
        public boolean validateObject(PooledObject<IotMessage> p) {
            // 验证对象是否有效
            return p.getObject() != null;
        }

        @Override
        public void passivateObject(PooledObject<IotMessage> p) throws Exception {
            // 对象归还时的处理
            if (p.getObject() != null) {
                p.getObject().reset();
            }
        }

        @Override
        public void activateObject(PooledObject<IotMessage> p) throws Exception {
            // 对象借用时的处理
            if (p.getObject() != null) {
                p.getObject().reset();
            }
        }
    }
}
