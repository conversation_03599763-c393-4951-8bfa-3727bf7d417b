<?xml version="1.0" encoding="UTF-8"?>
<!-- IoT模块独立日志配置 -->
<included>

    <!-- IoT模块日志路径变量 -->
    <property name="IOT_LOG_PATH" value="${log.path}/iot"/>

    <!-- IoT模块日志格式 -->
    <property name="IOT_LOG_PATTERN" value="%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level [IOT] %logger{50} - %msg%n"/>

    <!-- ==================== IoT模块Appender配置 ==================== -->

    <!-- IoT设备连接日志 -->
    <appender name="IOT_CONNECTION" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${IOT_LOG_PATH}/connection.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>${IOT_LOG_PATH}/connection.%d{yyyy-MM-dd}.%i.log</fileNamePattern>
            <timeBasedFileNamingAndTriggeringPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedFNATP">
                <maxFileSize>100MB</maxFileSize>
            </timeBasedFileNamingAndTriggeringPolicy>
            <maxHistory>30</maxHistory>
            <totalSizeCap>3GB</totalSizeCap>
        </rollingPolicy>
        <encoder>
            <pattern>${IOT_LOG_PATTERN}</pattern>
            <charset>UTF-8</charset>
        </encoder>
        <!-- 过滤连接相关日志 -->
        <filter class="ch.qos.logback.core.filter.EvaluatorFilter">
            <evaluator class="ch.qos.logback.classic.boolex.OnMarkerEvaluator">
                <marker>IOT_CONNECTION</marker>
            </evaluator>
            <onMismatch>NEUTRAL</onMismatch>
            <onMatch>ACCEPT</onMatch>
        </filter>
    </appender>

    <!-- IoT协议处理日志 -->
    <appender name="IOT_PROTOCOL" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${IOT_LOG_PATH}/protocol.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>${IOT_LOG_PATH}/protocol.%d{yyyy-MM-dd}.%i.log</fileNamePattern>
            <timeBasedFileNamingAndTriggeringPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedFNATP">
                <maxFileSize>100MB</maxFileSize>
            </timeBasedFileNamingAndTriggeringPolicy>
            <maxHistory>30</maxHistory>
            <totalSizeCap>3GB</totalSizeCap>
        </rollingPolicy>
        <encoder>
            <pattern>${IOT_LOG_PATTERN}</pattern>
            <charset>UTF-8</charset>
        </encoder>
        <!-- 过滤协议相关日志 -->
        <filter class="ch.qos.logback.core.filter.EvaluatorFilter">
            <evaluator class="ch.qos.logback.classic.boolex.OnMarkerEvaluator">
                <marker>IOT_PROTOCOL</marker>
            </evaluator>
            <onMismatch>NEUTRAL</onMismatch>
            <onMatch>ACCEPT</onMatch>
        </filter>
    </appender>

    <!-- IoT指令处理日志 -->
    <appender name="IOT_COMMAND" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${IOT_LOG_PATH}/command.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>${IOT_LOG_PATH}/command.%d{yyyy-MM-dd}.%i.log</fileNamePattern>
            <timeBasedFileNamingAndTriggeringPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedFNATP">
                <maxFileSize>100MB</maxFileSize>
            </timeBasedFileNamingAndTriggeringPolicy>
            <maxHistory>30</maxHistory>
            <totalSizeCap>3GB</totalSizeCap>
        </rollingPolicy>
        <encoder>
            <pattern>${IOT_LOG_PATTERN}</pattern>
            <charset>UTF-8</charset>
        </encoder>
        <!-- 过滤指令相关日志 -->
        <filter class="ch.qos.logback.core.filter.EvaluatorFilter">
            <evaluator class="ch.qos.logback.classic.boolex.OnMarkerEvaluator">
                <marker>IOT_COMMAND</marker>
            </evaluator>
            <onMismatch>NEUTRAL</onMismatch>
            <onMatch>ACCEPT</onMatch>
        </filter>
    </appender>

    <!-- TCP服务器日志 -->
    <appender name="IOT_SERVER" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${IOT_LOG_PATH}/server.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>${IOT_LOG_PATH}/server.%d{yyyy-MM-dd}.%i.log</fileNamePattern>
            <timeBasedFileNamingAndTriggeringPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedFNATP">
                <maxFileSize>100MB</maxFileSize>
            </timeBasedFileNamingAndTriggeringPolicy>
            <maxHistory>30</maxHistory>
            <totalSizeCap>3GB</totalSizeCap>
        </rollingPolicy>
        <encoder>
            <pattern>${IOT_LOG_PATTERN}</pattern>
            <charset>UTF-8</charset>
        </encoder>
        <!-- 过滤服务器相关日志 -->
        <filter class="ch.qos.logback.core.filter.EvaluatorFilter">
            <evaluator class="ch.qos.logback.classic.boolex.OnMarkerEvaluator">
                <marker>IOT_SERVER</marker>
            </evaluator>
            <onMismatch>NEUTRAL</onMismatch>
            <onMatch>ACCEPT</onMatch>
        </filter>
    </appender>

    <!-- IoT模块总日志 -->
    <appender name="IOT_ALL" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${IOT_LOG_PATH}/iot-all.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>${IOT_LOG_PATH}/iot-all.%d{yyyy-MM-dd}.%i.log</fileNamePattern>
            <timeBasedFileNamingAndTriggeringPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedFNATP">
                <maxFileSize>200MB</maxFileSize>
            </timeBasedFileNamingAndTriggeringPolicy>
            <maxHistory>15</maxHistory>
            <totalSizeCap>5GB</totalSizeCap>
        </rollingPolicy>
        <encoder>
            <pattern>${IOT_LOG_PATTERN}</pattern>
            <charset>UTF-8</charset>
        </encoder>
    </appender>

    <!-- ==================== IoT模块Logger配置 ==================== -->

    <!-- IoT模块总体配置 -->
    <logger name="com.yunqu.park.iot" level="INFO" additivity="false">
        <appender-ref ref="console"/>
        <appender-ref ref="IOT_CONNECTION"/>
        <appender-ref ref="IOT_PROTOCOL"/>
        <appender-ref ref="IOT_COMMAND"/>
        <appender-ref ref="IOT_SERVER"/>
        <appender-ref ref="IOT_ALL"/>
    </logger>

    <!-- IoT模块总体配置 -->
    <logger name="io.netty" level="DEBUG" additivity="false">
        <appender-ref ref="console"/>
        <appender-ref ref="IOT_CONNECTION"/>
        <appender-ref ref="IOT_PROTOCOL"/>
        <appender-ref ref="IOT_COMMAND"/>
        <appender-ref ref="IOT_SERVER"/>
        <appender-ref ref="IOT_ALL"/>
    </logger>

    <!-- 设备连接管理器 - 详细日志 -->
    <logger name="com.yunqu.park.iot.netty.manager.DeviceConnectionManager" level="DEBUG" additivity="false">
        <appender-ref ref="console"/>
        <appender-ref ref="IOT_CONNECTION"/>
        <appender-ref ref="IOT_ALL"/>
    </logger>

    <!-- 协议处理服务 - 详细日志 -->
    <logger name="com.yunqu.park.iot.service.impl.IotProtocolServiceImpl" level="DEBUG" additivity="false">
        <appender-ref ref="console"/>
        <appender-ref ref="IOT_PROTOCOL"/>
        <appender-ref ref="IOT_ALL"/>
    </logger>

    <!-- 指令服务 - 详细日志 -->
    <logger name="com.yunqu.park.iot.service.impl.IotCommandServiceImpl" level="DEBUG" additivity="false">
        <appender-ref ref="console"/>
        <appender-ref ref="IOT_COMMAND"/>
        <appender-ref ref="IOT_ALL"/>
    </logger>

    <!-- TCP服务器 - 详细日志 -->
    <logger name="com.yunqu.park.iot.netty.server.IotTcpServer" level="DEBUG" additivity="false">
        <appender-ref ref="console"/>
        <appender-ref ref="IOT_SERVER"/>
        <appender-ref ref="IOT_ALL"/>
    </logger>

    <!-- 消息处理器 - 信息日志 -->
    <logger name="com.yunqu.park.iot.netty.handler.IotMessageHandler" level="INFO" additivity="false">
        <appender-ref ref="console"/>
        <appender-ref ref="IOT_PROTOCOL"/>
        <appender-ref ref="IOT_ALL"/>
    </logger>

    <!-- 设备服务 - 信息日志 -->
    <logger name="com.yunqu.park.iot.service.impl.IotDeviceServiceImpl" level="INFO" additivity="false">
        <appender-ref ref="console"/>
        <appender-ref ref="IOT_CONNECTION"/>
        <appender-ref ref="IOT_ALL"/>
    </logger>

    <!-- 位置数据服务 - 信息日志 -->
    <logger name="com.yunqu.park.iot.service.impl.IotLocationDataServiceImpl" level="INFO" additivity="false">
        <appender-ref ref="console"/>
        <appender-ref ref="IOT_PROTOCOL"/>
        <appender-ref ref="IOT_ALL"/>
    </logger>

    <!-- 报警记录服务 - 信息日志 -->
    <logger name="com.yunqu.park.iot.service.impl.IotAlarmRecordServiceImpl" level="INFO" additivity="false">
        <appender-ref ref="console"/>
        <appender-ref ref="IOT_PROTOCOL"/>
        <appender-ref ref="IOT_ALL"/>
    </logger>

    <!-- 协议编解码器 - 警告日志 -->
    <logger name="com.yunqu.park.iot.netty.codec" level="DEBUG" additivity="false">
        <appender-ref ref="console"/>
        <appender-ref ref="IOT_PROTOCOL"/>
        <appender-ref ref="IOT_ALL"/>
    </logger>

    <!-- Netty框架 - 警告日志 -->
    <logger name="io.netty" level="DEBUG" additivity="false">
        <appender-ref ref="console"/>
        <appender-ref ref="IOT_ALL"/>
    </logger>

</included>
