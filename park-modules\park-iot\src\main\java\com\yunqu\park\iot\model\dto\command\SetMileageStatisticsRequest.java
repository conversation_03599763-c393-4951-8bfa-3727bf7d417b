package com.yunqu.park.iot.model.dto.command;

import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 设置里程统计请求
 * 
 * <AUTHOR>
 * @since 2025-01-06
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class SetMileageStatisticsRequest extends BaseCommandReq {
    
    /**
     * 是否启用里程统计
     * true: 启用, false: 禁用
     */
    @NotNull(message = "里程统计开关不能为空")
    private Boolean enabled;
}