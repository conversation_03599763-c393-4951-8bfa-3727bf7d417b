package com.yunqu.park.iot.model.dto.command;

import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.Max;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 切换里程模式请求
 * 
 * <AUTHOR>
 * @since 2025-01-06
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class SwitchMileageModeRequest extends BaseCommandReq {
    
    /**
     * 里程模式
     * 0: GPS里程, 1: 脉冲里程
     */
    @NotNull(message = "里程模式不能为空")
    @Min(value = 0, message = "里程模式值不能小于0")
    @Max(value = 1, message = "里程模式值不能大于1")
    private Integer mode;
}