-- ----------------------------
-- IoT物联网设备管理系统数据库初始化脚本
-- 基于GT06物联网设备协议对接详细设计文档
-- 创建时间: 2024-12-19
-- ----------------------------

-- ----------------------------
-- IoT设备基础信息表
-- ----------------------------
CREATE TABLE iot_device (
    device_id BIGINT PRIMARY KEY COMMENT '设备ID',
    imei VARCHAR(15) UNIQUE NOT NULL COMMENT '设备IMEI号',
    device_name VARCHAR(100) COMMENT '设备名称',
    device_type VARCHAR(50) DEFAULT 'GT06' COMMENT '设备类型',
    sim_imsi VARCHAR(15) COMMENT 'SIM卡IMSI号',
    sim_iccid VARCHAR(20) COMMENT 'SIM卡ICCID号',
    status CHAR(1) DEFAULT '0' COMMENT '设备状态:0-离线,1-在线,2-休眠',
    last_online_time DATETIME COMMENT '最后在线时间',
    register_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '注册时间',
    remark VARCHAR(500) COMMENT '备注',
    tenant_id VARCHAR(20) COMMENT '租户编号',
    create_dept BIGINT COMMENT '创建部门',
    create_by BIGINT COMMENT '创建者',
    create_time DATETIME COMMENT '创建时间',
    update_by BIGINT COMMENT '更新者',
    update_time DATETIME COMMENT '更新时间',
    del_flag CHAR(1) DEFAULT '0' COMMENT '删除标志(0代表存在 1代表删除)',
    INDEX idx_imei (imei),
    INDEX idx_device_type (device_type),
    INDEX idx_status (status),
    INDEX idx_tenant_id (tenant_id)
) ENGINE=InnoDB COMMENT='IoT设备基础信息表';

-- ----------------------------
-- 设备定位数据表
-- ----------------------------
CREATE TABLE iot_location_data (
    location_id BIGINT PRIMARY KEY COMMENT '定位记录ID',
    device_id BIGINT NOT NULL COMMENT '设备ID',
    imei VARCHAR(15) NOT NULL COMMENT '设备IMEI',
    gps_time DATETIME NOT NULL COMMENT 'GPS时间',
    latitude DECIMAL(10,6) COMMENT '纬度',
    longitude DECIMAL(10,6) COMMENT '经度',
    altitude INT COMMENT '海拔高度(米)',
    speed TINYINT COMMENT '速度(km/h)',
    direction SMALLINT COMMENT '方向角(0-360度)',
    satellite_count TINYINT COMMENT '卫星数量',
    gps_status CHAR(1) DEFAULT '0' COMMENT 'GPS状态:0-未定位,1-已定位',
    acc_status CHAR(1) DEFAULT '0' COMMENT 'ACC状态:0-关闭,1-开启',
    mcc SMALLINT COMMENT '移动国家代码',
    mnc TINYINT COMMENT '移动网络代码',
    lac SMALLINT COMMENT '位置区码',
    cell_id INT COMMENT '基站ID',
    signal_strength TINYINT COMMENT '信号强度(0-100)',
    mileage INT COMMENT '里程(米)',
    tenant_id VARCHAR(20) COMMENT '租户编号',
    create_time DATETIME COMMENT '创建时间',
    INDEX idx_device_time (device_id, gps_time),
    INDEX idx_imei_time (imei, gps_time),
    INDEX idx_tenant_time (tenant_id, gps_time),
    INDEX idx_gps_time (gps_time)
) ENGINE=InnoDB COMMENT='设备定位数据表';

-- ----------------------------
-- 设备报警记录表
-- ----------------------------
CREATE TABLE iot_alarm_record (
    alarm_id BIGINT PRIMARY KEY COMMENT '报警记录ID',
    device_id BIGINT NOT NULL COMMENT '设备ID',
    imei VARCHAR(15) NOT NULL COMMENT '设备IMEI',
    alarm_type TINYINT NOT NULL COMMENT '报警类型',
    alarm_name VARCHAR(50) COMMENT '报警名称',
    alarm_time DATETIME NOT NULL COMMENT '报警时间',
    latitude DECIMAL(10,6) COMMENT '报警位置纬度',
    longitude DECIMAL(10,6) COMMENT '报警位置经度',
    address VARCHAR(200) COMMENT '报警地址',
    alarm_status CHAR(1) DEFAULT '0' COMMENT '处理状态:0-未处理,1-已处理',
    handle_time DATETIME COMMENT '处理时间',
    handle_user BIGINT COMMENT '处理人',
    handle_remark VARCHAR(500) COMMENT '处理备注',
    tenant_id VARCHAR(20) COMMENT '租户编号',
    create_dept BIGINT COMMENT '创建部门',
    create_by BIGINT COMMENT '创建者',
    create_time DATETIME COMMENT '创建时间',
    update_by BIGINT COMMENT '更新者',
    update_time DATETIME COMMENT '更新时间',
    INDEX idx_device_time (device_id, alarm_time),
    INDEX idx_type_status (alarm_type, alarm_status),
    INDEX idx_tenant_time (tenant_id, alarm_time),
    INDEX idx_alarm_time (alarm_time)
) ENGINE=InnoDB COMMENT='设备报警记录表';

-- ----------------------------
-- 设备状态日志表
-- ----------------------------
CREATE TABLE iot_device_status_log (
    log_id BIGINT PRIMARY KEY COMMENT '日志ID',
    device_id BIGINT NOT NULL COMMENT '设备ID',
    imei VARCHAR(15) NOT NULL COMMENT '设备IMEI',
    old_status TINYINT COMMENT '原状态',
    new_status TINYINT COMMENT '新状态',
    change_reason VARCHAR(100) COMMENT '状态变更原因',
    battery_level TINYINT COMMENT '电池电量等级',
    signal_strength TINYINT COMMENT '信号强度',
    external_voltage TINYINT COMMENT '外部电压',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    INDEX idx_device_time (device_id, create_time),
    INDEX idx_imei_time (imei, create_time)
) ENGINE=InnoDB COMMENT='设备状态变更日志表';

-- ----------------------------
-- 初始化数据
-- ----------------------------

-- 插入测试设备数据
INSERT INTO iot_device (device_id, imei, device_name, device_type, status, register_time, tenant_id, create_time) VALUES
(1, '123456789012345', 'GT06测试设备001', 'GT06', '0', NOW(), '000000', NOW()),
(2, '123456789012346', 'GT06测试设备002', 'GT06', '0', NOW(), '000000', NOW());

-- 创建索引优化查询性能
-- 定位数据表分区索引(按月分区的准备)
-- ALTER TABLE iot_location_data PARTITION BY RANGE (YEAR(gps_time) * 100 + MONTH(gps_time)) (
--     PARTITION p202412 VALUES LESS THAN (202501),
--     PARTITION p202501 VALUES LESS THAN (202502),
--     PARTITION p_future VALUES LESS THAN MAXVALUE
-- );

-- 报警记录表复合索引
CREATE INDEX idx_alarm_device_type_time ON iot_alarm_record (device_id, alarm_type, alarm_time);

-- 定位数据表覆盖索引
CREATE INDEX idx_location_device_time_covering ON iot_location_data (device_id, gps_time) 
INCLUDE (latitude, longitude, speed, direction);

-- 设备表状态索引
CREATE INDEX idx_device_status_online_time ON iot_device (status, last_online_time);
