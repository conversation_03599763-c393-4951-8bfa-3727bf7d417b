package com.yunqu.park.iot.test;

import com.yunqu.park.common.core.domain.R;
import com.yunqu.park.iot.utils.CrcUtils;
import com.yunqu.park.iot.utils.ByteBufUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;

/**
 * CRC校验测试控制器
 * 用于测试和调试CRC校验功能
 *
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/iot/test/crc")
public class CrcTestController {

    /**
     * 测试CRC计算
     */
    @PostMapping("/calculate")
    public R<Map<String, Object>> calculateCrc(@RequestBody CrcTestRequest request) {
        Map<String, Object> result = new HashMap<>();
        
        try {
            byte[] data = hexStringToBytes(request.getHexData());
            
            result.put("originalHex", request.getHexData());
            result.put("dataLength", data.length);
            result.put("data", ByteBufUtils.bytesToHexString(data));
            
            // 计算CRC
            int crc = CrcUtils.calculateCrcItu(data);
            result.put("calculatedCrc", String.format("0x%04X", crc));
            result.put("crcHighByte", String.format("0x%02X", (crc >> 8) & 0xFF));
            result.put("crcLowByte", String.format("0x%02X", crc & 0xFF));
            
            // 添加CRC到数据
            byte[] dataWithCrc = CrcUtils.addCrc(data);
            result.put("dataWithCrc", ByteBufUtils.bytesToHexString(dataWithCrc));
            
            result.put("success", true);
            
        } catch (Exception e) {
            log.error("CRC calculation failed: {}", e.getMessage(), e);
            result.put("error", e.getMessage());
            result.put("success", false);
        }
        
        return R.ok(result);
    }

    /**
     * 测试CRC校验
     */
    @PostMapping("/validate")
    public R<Map<String, Object>> validateCrc(@RequestBody CrcTestRequest request) {
        Map<String, Object> result = new HashMap<>();
        
        try {
            byte[] data = hexStringToBytes(request.getHexData());
            
            result.put("originalHex", request.getHexData());
            result.put("dataLength", data.length);
            result.put("data", ByteBufUtils.bytesToHexString(data));
            
            if (data.length < 4) {
                result.put("error", "Data too short for CRC validation (minimum 4 bytes)");
                result.put("success", false);
                return R.ok(result);
            }
            
            // 分离数据和CRC
            byte[] dataOnly = new byte[data.length - 2];
            System.arraycopy(data, 0, dataOnly, 0, dataOnly.length);
            
            byte crcHighByte = data[data.length - 2];
            byte crcLowByte = data[data.length - 1];
            
            result.put("dataOnly", ByteBufUtils.bytesToHexString(dataOnly));
            result.put("receivedCrcBytes", String.format("0x%02X 0x%02X", crcHighByte & 0xFF, crcLowByte & 0xFF));
            
            // 解析接收到的CRC（尝试两种字节序）
            int receivedCrcBigEndian = ((crcHighByte & 0xFF) << 8) | (crcLowByte & 0xFF);
            int receivedCrcLittleEndian = ((crcLowByte & 0xFF) << 8) | (crcHighByte & 0xFF);
            
            result.put("receivedCrcBigEndian", String.format("0x%04X", receivedCrcBigEndian));
            result.put("receivedCrcLittleEndian", String.format("0x%04X", receivedCrcLittleEndian));
            
            // 计算CRC
            int calculatedCrc = CrcUtils.calculateCrcItu(dataOnly);
            result.put("calculatedCrc", String.format("0x%04X", calculatedCrc));
            
            // 校验结果
            boolean validBigEndian = calculatedCrc == receivedCrcBigEndian;
            boolean validLittleEndian = calculatedCrc == receivedCrcLittleEndian;
            
            result.put("validBigEndian", validBigEndian);
            result.put("validLittleEndian", validLittleEndian);
            
            // 使用标准校验方法
            boolean standardValid = CrcUtils.validateCrc(data);
            result.put("standardValidation", standardValid);
            
            // 使用宽松校验方法
            boolean lenientValid = CrcUtils.validateCrcLenient(data);
            result.put("lenientValidation", lenientValid);
            
            result.put("overallValid", standardValid || lenientValid);
            result.put("success", true);
            
        } catch (Exception e) {
            log.error("CRC validation failed: {}", e.getMessage(), e);
            result.put("error", e.getMessage());
            result.put("success", false);
        }
        
        return R.ok(result);
    }

    /**
     * 测试GT06协议数据包的CRC
     */
    @PostMapping("/validate-gt06-packet")
    public R<Map<String, Object>> validateGt06Packet(@RequestBody CrcTestRequest request) {
        Map<String, Object> result = new HashMap<>();
        
        try {
            byte[] fullPacket = hexStringToBytes(request.getHexData());
            
            result.put("originalHex", request.getHexData());
            result.put("fullPacketLength", fullPacket.length);
            
            if (fullPacket.length < 8) { // 最小GT06包：起始位(2) + 长度(1) + 协议(1) + 序列号(2) + CRC(2) + 停止位(2)
                result.put("error", "Packet too short for GT06 protocol");
                result.put("success", false);
                return R.ok(result);
            }
            
            // 解析GT06包结构
            byte[] startFlag = {fullPacket[0], fullPacket[1]};
            byte packetLength = fullPacket[2];
            
            result.put("startFlag", ByteBufUtils.bytesToHexString(startFlag));
            result.put("packetLength", String.format("0x%02X (%d)", packetLength & 0xFF, packetLength & 0xFF));
            
            // 提取包内容（不含起始位、长度字节和停止位）
            int contentLength = packetLength & 0xFF;
            if (fullPacket.length < 3 + contentLength + 2) {
                result.put("error", "Packet length mismatch");
                result.put("success", false);
                return R.ok(result);
            }
            
            byte[] packetContent = new byte[contentLength];
            System.arraycopy(fullPacket, 3, packetContent, 0, contentLength);
            
            byte[] stopFlag = {fullPacket[fullPacket.length - 2], fullPacket[fullPacket.length - 1]};
            
            result.put("packetContent", ByteBufUtils.bytesToHexString(packetContent));
            result.put("stopFlag", ByteBufUtils.bytesToHexString(stopFlag));
            
            // 校验CRC
            boolean crcValid = CrcUtils.validateCrcLenient(packetContent);
            result.put("crcValid", crcValid);
            
            // 详细CRC分析
            if (packetContent.length >= 4) {
                byte[] dataOnly = new byte[packetContent.length - 2];
                System.arraycopy(packetContent, 0, dataOnly, 0, dataOnly.length);
                
                int calculatedCrc = CrcUtils.calculateCrcItu(dataOnly);
                int receivedCrc = ((packetContent[packetContent.length - 1] & 0xFF) << 8) | 
                                 (packetContent[packetContent.length - 2] & 0xFF);
                
                result.put("calculatedCrc", String.format("0x%04X", calculatedCrc));
                result.put("receivedCrc", String.format("0x%04X", receivedCrc));
                result.put("crcMatch", calculatedCrc == receivedCrc);
            }
            
            result.put("success", true);
            
        } catch (Exception e) {
            log.error("GT06 packet CRC validation failed: {}", e.getMessage(), e);
            result.put("error", e.getMessage());
            result.put("success", false);
        }
        
        return R.ok(result);
    }

    /**
     * 生成测试数据包
     */
    @PostMapping("/generate-test-packet")
    public R<Map<String, Object>> generateTestPacket(@RequestBody GeneratePacketRequest request) {
        Map<String, Object> result = new HashMap<>();
        
        try {
            // 构建测试数据
            byte[] testData = hexStringToBytes(request.getDataHex());
            
            // 添加CRC
            byte[] dataWithCrc = CrcUtils.addCrc(testData);
            
            // 构建完整的GT06包
            byte[] fullPacket = new byte[2 + 1 + dataWithCrc.length + 2];
            
            // 起始位
            fullPacket[0] = 0x78;
            fullPacket[1] = 0x78;
            
            // 包长度
            fullPacket[2] = (byte) dataWithCrc.length;
            
            // 包内容（含CRC）
            System.arraycopy(dataWithCrc, 0, fullPacket, 3, dataWithCrc.length);
            
            // 停止位
            fullPacket[fullPacket.length - 2] = 0x0D;
            fullPacket[fullPacket.length - 1] = 0x0A;
            
            result.put("originalData", request.getDataHex());
            result.put("dataWithCrc", ByteBufUtils.bytesToHexString(dataWithCrc));
            result.put("fullPacket", ByteBufUtils.bytesToHexString(fullPacket));
            result.put("packetLength", fullPacket.length);
            result.put("success", true);
            
        } catch (Exception e) {
            log.error("Test packet generation failed: {}", e.getMessage(), e);
            result.put("error", e.getMessage());
            result.put("success", false);
        }
        
        return R.ok(result);
    }

    /**
     * 将十六进制字符串转换为字节数组
     */
    private byte[] hexStringToBytes(String hexString) {
        if (hexString == null || hexString.isEmpty()) {
            return new byte[0];
        }
        
        // 移除空格和其他分隔符
        hexString = hexString.replaceAll("[\\s-:]", "");
        
        if (hexString.length() % 2 != 0) {
            throw new IllegalArgumentException("Hex string length must be even");
        }
        
        byte[] bytes = new byte[hexString.length() / 2];
        for (int i = 0; i < bytes.length; i++) {
            bytes[i] = (byte) Integer.parseInt(hexString.substring(i * 2, i * 2 + 2), 16);
        }
        
        return bytes;
    }

    /**
     * CRC测试请求对象
     */
    public static class CrcTestRequest {
        private String hexData;
        
        public String getHexData() { return hexData; }
        public void setHexData(String hexData) { this.hexData = hexData; }
    }

    /**
     * 生成数据包请求对象
     */
    public static class GeneratePacketRequest {
        private String dataHex;
        
        public String getDataHex() { return dataHex; }
        public void setDataHex(String dataHex) { this.dataHex = dataHex; }
    }
}
