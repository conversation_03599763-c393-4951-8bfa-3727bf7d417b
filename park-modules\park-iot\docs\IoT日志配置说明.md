# IoT模块日志配置说明

## 模块化日志配置架构

IoT模块采用模块化的日志配置管理方式：

1. **模块独立配置**：`park-modules/park-iot/src/main/resources/logback-iot-config.xml`
2. **主应用引入**：在`park-admin/src/main/resources/logback-plus.xml`中通过`<include>`引入
3. **标记分类**：使用SLF4J Marker进行日志分类和过滤

## 配置文件结构

```
park-admin/src/main/resources/
├── logback-plus.xml                    # 主应用日志配置
│   └── <include resource="logback-iot-config.xml"/>  # 引入IoT模块配置

park-modules/park-iot/src/main/resources/
├── logback-iot-config.xml              # IoT模块独立日志配置
└── application-iot.yml                 # IoT模块应用配置
```

## 日志文件结构

IoT模块的日志文件将输出到以下目录结构：

```
logs/
├── iot/
│   ├── connection.log          # 设备连接日志
│   ├── protocol.log           # 协议处理日志
│   ├── command.log            # 指令处理日志
│   ├── server.log             # TCP服务器日志
│   └── iot-all.log            # IoT模块总日志
├── sys-console.log            # 系统控制台日志
├── sys-info.log              # 系统信息日志
└── sys-error.log             # 系统错误日志
```

## 日志分类说明

### 1. 设备连接日志 (connection.log)
记录设备连接相关的所有操作：
- `[CONNECTION-REGISTER]` - 设备连接注册
- `[CONNECTION-REMOVE]` - 设备连接移除
- `[CHANNEL-ACTIVE]` - 通道激活
- `[CHANNEL-INACTIVE]` - 通道失活
- `[DEVICE-ONLINE]` - 设备上线
- `[DEVICE-OFFLINE]` - 设备离线

### 2. 协议处理日志 (protocol.log)
记录协议解析和消息处理：
- `[PROTOCOL-LOGIN]` - 设备登录协议
- `[PROTOCOL-HEARTBEAT]` - 心跳协议
- `[PROTOCOL-LOCATION]` - 位置数据协议
- `[MESSAGE-RECEIVED]` - 消息接收
- `[PROTOCOL-DECODE]` - 协议解码

### 3. 指令处理日志 (command.log)
记录设备指令相关操作：
- `[COMMAND-RESTART]` - 重启指令
- `[COMMAND-RESET]` - 复位指令
- `[COMMAND-SEND]` - 指令发送
- `[COMMAND-INTERVAL]` - 间隔设置
- `[COMMAND-APN]` - APN配置

### 4. TCP服务器日志 (server.log)
记录TCP服务器运行状态：
- `[TCP-SERVER]` - 服务器启动、关闭、配置

### 5. IoT模块总日志 (iot-all.log)
包含所有IoT模块的日志信息，用于全面追踪。

## 日志级别配置

### 开发环境 (dev/test)
- 控制台输出：DEBUG级别
- 文件输出：DEBUG级别
- 详细的调试信息

### 生产环境 (prod)
- 控制台输出：关闭
- 文件输出：INFO级别
- 关键信息和错误

## 日志格式

```
%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level [IOT] %logger{50} - %msg%n
```

示例：
```
2024-01-15 10:30:45.123 [iot-worker-1] INFO  [IOT] c.y.p.i.n.m.DeviceConnectionManager - [CONNECTION-REGISTER] 🔗 Device connection registered successfully: IMEI=123456789012345, ChannelId=abc123, TotalConnections=5
```

## 日志轮转配置

- **文件大小限制**：单个日志文件最大100MB（iot-all.log为200MB）
- **保留时间**：connection/protocol/command/server日志保留30天，iot-all日志保留15天
- **总大小限制**：各分类日志总大小3GB，iot-all日志总大小5GB
- **轮转方式**：按日期和大小轮转

## 使用测试接口

为了验证日志配置是否生效，可以使用以下测试接口：

```bash
# 测试所有类型日志
GET /iot/test/log/test-all

# 测试连接日志
GET /iot/test/log/test-connection

# 测试协议日志
GET /iot/test/log/test-protocol

# 测试指令日志
GET /iot/test/log/test-command

# 测试服务器日志
GET /iot/test/log/test-server

# 获取统计信息
GET /iot/test/log/statistics
```

## 监控接口

```bash
# 获取连接统计
GET /iot/monitor/connections

# 获取系统统计
GET /iot/monitor/statistics

# 获取系统健康状态
GET /iot/monitor/health

# 检查设备在线状态
GET /iot/monitor/device/{imei}/online
```

## 日志追踪示例

完整的设备生命周期日志追踪：

```
2024-01-15 10:30:45.123 [iot-worker-1] INFO  [IOT] IotMessageHandler - [CHANNEL-ACTIVE] 🔗 New device connection established: RemoteAddress=/*************:12345, ChannelId=abc123
2024-01-15 10:30:45.125 [iot-worker-1] INFO  [IOT] IotProtocolServiceImpl - [PROTOCOL-LOGIN] 🔐 Processing device login: IMEI=123456789012345, ClientIP=*************
2024-01-15 10:30:45.127 [iot-worker-1] INFO  [IOT] DeviceConnectionManager - [CONNECTION-REGISTER] ✅ Device connection registered successfully: IMEI=123456789012345, ChannelId=abc123
2024-01-15 10:30:45.130 [iot-worker-1] INFO  [IOT] IotDeviceServiceImpl - [DEVICE-ONLINE] ✅ Device successfully online: IMEI=123456789012345, ClientIP=*************
2024-01-15 10:31:00.456 [iot-worker-1] DEBUG [IOT] IotProtocolServiceImpl - [PROTOCOL-HEARTBEAT] 💓 Received heartbeat: IMEI=123456789012345, SequenceNumber=1
2024-01-15 10:31:15.789 [iot-worker-1] INFO  [IOT] IotProtocolServiceImpl - [PROTOCOL-LOCATION] 📍 Processing location data: IMEI=123456789012345, SequenceNumber=2
2024-01-15 10:32:00.123 [http-nio-9080-exec-1] INFO  [IOT] IotCommandServiceImpl - [COMMAND-RESTART] 🔄 Processing restart command: IMEI=123456789012345
2024-01-15 10:32:00.125 [http-nio-9080-exec-1] INFO  [IOT] DeviceConnectionManager - [COMMAND-SEND] ✅ Command sent successfully: IMEI=123456789012345, Command=RESET#
2024-01-15 10:35:30.999 [iot-worker-1] INFO  [IOT] IotMessageHandler - [CHANNEL-INACTIVE] 🔌 Device connection closed: RemoteAddress=/*************:12345, ChannelId=abc123
2024-01-15 10:35:31.001 [iot-worker-1] INFO  [IOT] IotDeviceServiceImpl - [DEVICE-OFFLINE] ✅ Device successfully offline: IMEI=123456789012345
```

## 日志标记系统

IoT模块使用SLF4J Marker进行日志分类：

```java
// 日志标记常量
public class IotLogMarkers {
    public static final Marker IOT_CONNECTION = MarkerFactory.getMarker("IOT_CONNECTION");
    public static final Marker IOT_PROTOCOL = MarkerFactory.getMarker("IOT_PROTOCOL");
    public static final Marker IOT_COMMAND = MarkerFactory.getMarker("IOT_COMMAND");
    public static final Marker IOT_SERVER = MarkerFactory.getMarker("IOT_SERVER");
}

// 使用示例
log.info(IotLogMarkers.IOT_CONNECTION, "[CONNECTION-REGISTER] Device connected: {}", imei);
```

## 配置管理

### 自动配置检查
- 应用启动时自动检查日志配置
- 自动创建日志目录
- 验证Logger和Appender配置

### 配置状态监控
```bash
# 获取日志配置状态
GET /iot/monitor/log/config
```

## 模块化优势

1. **独立维护**：IoT模块可以独立管理自己的日志配置
2. **配置隔离**：不影响主应用的日志配置
3. **灵活引入**：主应用可以选择性引入模块日志配置
4. **版本控制**：模块日志配置随模块代码一起版本管理

## 注意事项

1. **配置文件位置**：
   - 模块配置：`park-modules/park-iot/src/main/resources/logback-iot-config.xml`
   - 主应用引入：`park-admin/src/main/resources/logback-plus.xml`
2. **标记使用**：推荐使用日志标记进行分类，便于过滤和管理
3. **日志目录**：确保应用有权限创建 `logs/iot/` 目录
4. **性能考虑**：生产环境建议使用INFO级别，避免过多DEBUG日志影响性能
5. **磁盘空间**：定期清理旧日志文件，避免磁盘空间不足
6. **配置更新**：修改模块日志配置后需要重启应用生效
