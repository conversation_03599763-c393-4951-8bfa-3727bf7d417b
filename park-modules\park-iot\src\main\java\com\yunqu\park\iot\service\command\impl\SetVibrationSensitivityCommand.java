package com.yunqu.park.iot.service.command.impl;

import com.yunqu.park.iot.model.command.CommandType;
import com.yunqu.park.iot.model.command.dto.SetVibrationSensitivityParams;
import com.yunqu.park.iot.service.command.ICommand;
import lombok.RequiredArgsConstructor;

@RequiredArgsConstructor
public class SetVibrationSensitivityCommand implements ICommand {

    private final SetVibrationSensitivityParams params;
    private static final String TEMPLATE = "SENLEVEL,%d#";

    @Override
    public CommandType getType() {
        return CommandType.SET_VIBRATION_SENSITIVITY;
    }

    @Override
    public String build() {
        return String.format(TEMPLATE, params.getLevel());
    }

    @Override
    public String getDescription() {
        return String.format("%s: 等级%d", getType().getDescription(), params.getLevel());
    }
}