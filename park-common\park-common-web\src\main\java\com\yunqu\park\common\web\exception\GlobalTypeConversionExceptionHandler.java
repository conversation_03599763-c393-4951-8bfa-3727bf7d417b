package com.yunqu.park.common.web.exception;

import com.yunqu.park.common.core.domain.R;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.TypeMismatchException;
import org.springframework.core.annotation.Order;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestControllerAdvice;
import org.springframework.web.method.annotation.MethodArgumentTypeMismatchException;

import java.time.format.DateTimeParseException;

/**
 * 全局类型转换异常处理器
 * 处理各种类型转换失败的情况
 *
 * <AUTHOR>
 */
@Slf4j
@Order(1) // 优先级高于其他异常处理器
@RestControllerAdvice
public class GlobalTypeConversionExceptionHandler {

    /**
     * 处理类型转换异常
     * 主要处理Date类型转换失败的情况
     */
    @ExceptionHandler(TypeMismatchException.class)
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    public R<Void> handleTypeMismatchException(TypeMismatchException e) {
        String fieldName = e.getPropertyName();
        Object value = e.getValue();
        Class<?> requiredType = e.getRequiredType();
        
        log.warn("Type conversion failed: field={}, value={}, requiredType={}, error={}", 
                fieldName, value, requiredType != null ? requiredType.getSimpleName() : "unknown", e.getMessage());
        
        // 特殊处理Date类型转换错误
        if (requiredType != null && isDateType(requiredType)) {
            return R.fail(String.format("日期字段 '%s' 的值 '%s' 格式不正确，请使用正确的日期格式（如：yyyy-MM-dd 或 yyyy-MM-dd HH:mm:ss）", fieldName, value));
        }
        
        // 特殊处理数字类型转换错误
        if (requiredType != null && isNumericType(requiredType)) {
            return R.fail(String.format("数字字段 '%s' 的值 '%s' 格式不正确，请输入有效的数字", fieldName, value));
        }
        
        return R.fail(String.format("字段 '%s' 的值 '%s' 类型不匹配，期望类型：%s", 
                fieldName, value, requiredType != null ? requiredType.getSimpleName() : "unknown"));
    }

    /**
     * 处理方法参数类型转换异常
     */
    @ExceptionHandler(MethodArgumentTypeMismatchException.class)
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    public R<Void> handleMethodArgumentTypeMismatchException(MethodArgumentTypeMismatchException e) {
        String paramName = e.getName();
        Object value = e.getValue();
        Class<?> requiredType = e.getRequiredType();
        
        log.warn("Method argument type conversion failed: param={}, value={}, requiredType={}, error={}", 
                paramName, value, requiredType != null ? requiredType.getSimpleName() : "unknown", e.getMessage());
        
        // 特殊处理Date类型转换错误
        if (requiredType != null && isDateType(requiredType)) {
            return R.fail(String.format("参数 '%s' 的值 '%s' 不是有效的日期格式", paramName, value));
        }
        
        // 特殊处理数字类型转换错误
        if (requiredType != null && isNumericType(requiredType)) {
            return R.fail(String.format("参数 '%s' 的值 '%s' 不是有效的数字", paramName, value));
        }
        
        return R.fail(String.format("参数 '%s' 的值 '%s' 类型不正确", paramName, value));
    }

    /**
     * 处理数字格式异常
     */
    @ExceptionHandler(NumberFormatException.class)
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    public R<Void> handleNumberFormatException(NumberFormatException e) {
        log.warn("Number format exception: {}", e.getMessage());
        return R.fail("数字格式不正确：" + e.getMessage());
    }

    /**
     * 处理日期解析异常
     */
    @ExceptionHandler(DateTimeParseException.class)
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    public R<Void> handleDateTimeParseException(DateTimeParseException e) {
        log.warn("Date time parse exception: {}", e.getMessage());
        return R.fail("日期时间格式不正确：" + e.getParsedString());
    }

    /**
     * 处理IllegalArgumentException
     */
    @ExceptionHandler(IllegalArgumentException.class)
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    public R<Void> handleIllegalArgumentException(IllegalArgumentException e) {
        log.warn("Illegal argument exception: {}", e.getMessage());
        return R.fail("参数错误：" + e.getMessage());
    }

    /**
     * 判断是否为日期类型
     */
    private boolean isDateType(Class<?> type) {
        return java.util.Date.class.isAssignableFrom(type) ||
               java.sql.Date.class.isAssignableFrom(type) ||
               java.sql.Timestamp.class.isAssignableFrom(type) ||
               java.time.LocalDate.class.isAssignableFrom(type) ||
               java.time.LocalDateTime.class.isAssignableFrom(type) ||
               java.time.ZonedDateTime.class.isAssignableFrom(type);
    }

    /**
     * 判断是否为数字类型
     */
    private boolean isNumericType(Class<?> type) {
        return Number.class.isAssignableFrom(type) ||
               type == int.class || type == Integer.class ||
               type == long.class || type == Long.class ||
               type == double.class || type == Double.class ||
               type == float.class || type == Float.class ||
               type == short.class || type == Short.class ||
               type == byte.class || type == Byte.class;
    }
}
