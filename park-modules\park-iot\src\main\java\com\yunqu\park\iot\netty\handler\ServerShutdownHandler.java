package com.yunqu.park.iot.netty.handler;

import io.netty.channel.ChannelHandlerContext;
import io.netty.channel.ChannelInboundHandlerAdapter;
import io.netty.util.AttributeKey;
import lombok.extern.slf4j.Slf4j;

/**
 * 服务器关闭处理器
 * 在服务器关闭时优雅地关闭所有活跃连接
 *
 * <AUTHOR>
 */
@Slf4j
public class ServerShutdownHandler extends ChannelInboundHandlerAdapter {

    // 使用与IotMessageHandler相同的AttributeKey
    private static final AttributeKey<DisconnectInitiator> DISCONNECT_INITIATOR =
            AttributeKey.valueOf("disconnect_initiator");
    private static final AttributeKey<DisconnectReason> DISCONNECT_REASON =
            AttributeKey.valueOf("disconnect_reason");

    @Override
    public void channelInactive(ChannelHandlerContext ctx) throws Exception {
        // 检查是否是服务器主动关闭
        if (ctx.channel().hasAttr(AttributeKey.valueOf("server_shutdown"))) {
            // 设置断开连接的主动方和原因
            ctx.channel().attr(DISCONNECT_INITIATOR).set(DisconnectInitiator.SERVER);
            ctx.channel().attr(DISCONNECT_REASON).set(DisconnectReason.SERVER_SHUTDOWN);

            log.debug("[SERVER-SHUTDOWN] 标记通道为服务器主动关闭: ChannelId={}", ctx.channel().id().asShortText());
        }

        // 继续处理链
        super.channelInactive(ctx);
    }

    @Override
    public void exceptionCaught(ChannelHandlerContext ctx, Throwable cause) throws Exception {
        log.warn("[SERVER-SHUTDOWN] 服务器关闭处理器异常: {}", cause.getMessage());
        super.exceptionCaught(ctx, cause);
    }
}
