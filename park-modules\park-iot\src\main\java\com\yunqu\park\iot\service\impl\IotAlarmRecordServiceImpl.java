package com.yunqu.park.iot.service.impl;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yunqu.park.common.core.utils.MapstructUtils;
import com.yunqu.park.common.mybatis.core.page.PageQuery;
import com.yunqu.park.common.mybatis.core.page.TableDataInfo;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yunqu.park.iot.constant.IotConstants;
import com.yunqu.park.iot.domain.IotAlarmRecord;
import com.yunqu.park.iot.domain.bo.IotAlarmRecordBo;
import com.yunqu.park.iot.domain.vo.IotAlarmRecordVo;
import com.yunqu.park.iot.mapper.IotAlarmRecordMapper;
import com.yunqu.park.iot.service.IIotAlarmRecordService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 设备报警记录Service业务层处理
 *
 * <AUTHOR>
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class IotAlarmRecordServiceImpl extends ServiceImpl<IotAlarmRecordMapper, IotAlarmRecord> implements IIotAlarmRecordService {

    private final IotAlarmRecordMapper baseMapper;

    /**
     * 查询设备报警记录
     */
    @Override
    public IotAlarmRecordVo queryById(Long alarmId) {
        IotAlarmRecord entity = baseMapper.selectById(alarmId);
        return entity != null ? MapstructUtils.convert(entity, IotAlarmRecordVo.class) : null;
    }

    /**
     * 查询设备报警记录列表
     */
    @Override
    public TableDataInfo<IotAlarmRecordVo> queryPageList(IotAlarmRecordBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<IotAlarmRecord> lqw = buildQueryWrapper(bo);
        Page<IotAlarmRecordVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询设备报警记录列表
     */
    @Override
    public List<IotAlarmRecordVo> queryList(IotAlarmRecordBo bo) {
        LambdaQueryWrapper<IotAlarmRecord> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<IotAlarmRecord> buildQueryWrapper(IotAlarmRecordBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<IotAlarmRecord> lqw = Wrappers.<IotAlarmRecord>lambdaQuery();
        lqw.eq(bo.getDeviceId() != null, IotAlarmRecord::getDeviceId, bo.getDeviceId());
        lqw.eq(StrUtil.isNotBlank(bo.getImei()), IotAlarmRecord::getImei, bo.getImei());
        lqw.eq(bo.getAlarmType() != null, IotAlarmRecord::getAlarmType, bo.getAlarmType());
        lqw.like(StrUtil.isNotBlank(bo.getAlarmName()), IotAlarmRecord::getAlarmName, bo.getAlarmName());
        lqw.eq(StrUtil.isNotBlank(bo.getAlarmStatus()), IotAlarmRecord::getAlarmStatus, bo.getAlarmStatus());
        lqw.between(params.get("beginAlarmTime") != null && params.get("endAlarmTime") != null,
                IotAlarmRecord::getAlarmTime, params.get("beginAlarmTime"), params.get("endAlarmTime"));
        lqw.orderByDesc(IotAlarmRecord::getAlarmTime);
        return lqw;
    }

    /**
     * 新增设备报警记录
     */
    @Override
    public Boolean insertByBo(IotAlarmRecordBo bo) {
        IotAlarmRecord add = MapstructUtils.convert(bo, IotAlarmRecord.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setAlarmId(add.getAlarmId());
        }
        return flag;
    }

    /**
     * 修改设备报警记录
     */
    @Override
    public Boolean updateByBo(IotAlarmRecordBo bo) {
        IotAlarmRecord update = MapstructUtils.convert(bo, IotAlarmRecord.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(IotAlarmRecord entity) {
        // 设置报警名称
        if (StrUtil.isBlank(entity.getAlarmName()) && entity.getAlarmType() != null) {
            entity.setAlarmName(getAlarmName(entity.getAlarmType()));
        }
    }

    /**
     * 批量删除设备报警记录
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            // 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteBatchIds(ids) > 0;
    }

    @Override
    public Boolean handleAlarm(Long alarmId, Long handleUser, String handleRemark) {
        try {
            baseMapper.updateAlarmHandleStatus(alarmId, handleUser, handleRemark);
            log.info("Alarm {} handled by user {}", alarmId, handleUser);
            return true;
        } catch (Exception e) {
            log.error("Failed to handle alarm {}: {}", alarmId, e.getMessage(), e);
            return false;
        }
    }

    @Override
    public List<IotAlarmRecordVo> getUnhandledAlarms(String imei) {
        try {
            return baseMapper.selectUnhandledAlarmsByImei(imei);
        } catch (Exception e) {
            log.error("Failed to get unhandled alarms for device {}: {}", imei, e.getMessage(), e);
            return List.of();
        }
    }

    @Override
    public Object getAlarmStatistics(Date startTime, Date endTime) {
        try {
            return baseMapper.selectAlarmStatistics(startTime, endTime);
        } catch (Exception e) {
            log.error("Failed to get alarm statistics: {}", e.getMessage(), e);
            return null;
        }
    }

    @Override
    public Boolean saveAlarmRecord(IotAlarmRecord alarmRecord) {
        try {
            // 设置报警名称
            if (StrUtil.isBlank(alarmRecord.getAlarmName()) && alarmRecord.getAlarmType() != null) {
                alarmRecord.setAlarmName(getAlarmName(alarmRecord.getAlarmType()));
            }
            
            // 设置默认状态为未处理
            if (StrUtil.isBlank(alarmRecord.getAlarmStatus())) {
                alarmRecord.setAlarmStatus(IotConstants.AlarmStatus.UNHANDLED);
            }
            
            boolean result = baseMapper.insert(alarmRecord) > 0;
            
            if (result) {
                log.info("Saved alarm record: device={}, type={}, time={}", 
                        alarmRecord.getImei(), alarmRecord.getAlarmType(), alarmRecord.getAlarmTime());
                
                // TODO: 推送报警信息到前端
                // pushAlarmNotification(alarmRecord);
            }
            
            return result;
        } catch (Exception e) {
            log.error("Failed to save alarm record for device {}: {}", alarmRecord.getImei(), e.getMessage(), e);
            return false;
        }
    }

    @Override
    public String getAlarmName(Integer alarmType) {
        if (alarmType == null) {
            return "未知报警";
        }
        
        return switch (alarmType) {
            case IotConstants.AlarmType.NORMAL -> "正常";
            case IotConstants.AlarmType.SOS -> "SOS报警";
            case IotConstants.AlarmType.POWER_CUT -> "断电报警";
            case IotConstants.AlarmType.VIBRATION -> "震动报警";
            case IotConstants.AlarmType.FENCE_IN -> "进围栏报警";
            case IotConstants.AlarmType.FENCE_OUT -> "出围栏报警";
            case IotConstants.AlarmType.OVERSPEED -> "超速报警";
            case IotConstants.AlarmType.HIGH_TEMP -> "高温报警";
            case IotConstants.AlarmType.LOW_TEMP -> "低温报警";
            case IotConstants.AlarmType.DISPLACEMENT -> "位移报警";
            case IotConstants.AlarmType.LOW_BATTERY -> "低电报警";
            case IotConstants.AlarmType.TAMPER -> "防拆报警";
            case IotConstants.AlarmType.RAPID_ACCELERATION -> "急加速报警";
            case IotConstants.AlarmType.RAPID_DECELERATION -> "急减速报警";
            case IotConstants.AlarmType.SHARP_TURN -> "急转弯报警";
            case IotConstants.AlarmType.COLLISION -> "碰撞报警";
            case IotConstants.AlarmType.DOOR_CLOSE -> "门关闭报警";
            case IotConstants.AlarmType.DOOR_OPEN -> "门打开报警";
            case IotConstants.AlarmType.AC_CLOSE -> "AC关闭报警";
            case IotConstants.AlarmType.AC_OPEN -> "AC打开报警";
            case IotConstants.AlarmType.ACC_ON -> "ACC点火报警";
            case IotConstants.AlarmType.ACC_OFF -> "ACC熄火报警";
            default -> "未知报警类型(" + String.format("0x%02X", alarmType) + ")";
        };
    }
}
