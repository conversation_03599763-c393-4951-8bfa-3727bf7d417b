package com.yunqu.park.iot.model.dto.command;

import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.Max;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 设置静止休眠请求
 * 
 * <AUTHOR>
 * @since 2025-01-06
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class SetStaticSleepRequest extends BaseCommandReq {
    
    /**
     * 是否启用静止休眠
     * true: 启用, false: 禁用
     */
    @NotNull(message = "静止休眠开关不能为空")
    private Boolean enabled;
    
    /**
     * 静止休眠时间 (分钟: 1-1440)
     */
    @Min(value = 1, message = "静止休眠时间不能小于1分钟")
    @Max(value = 1440, message = "静止休眠时间不能大于1440分钟")
    private Integer sleepTime;
}