package com.yunqu.park.iot.model.dto;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Map;

/**
 * 批量指令请求 DTO
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
public class BatchCommandRequest {

    /**
     * 设备IMEI号列表
     */
    private String[] imeis;

    /**
     * 指令类型
     */
    private String commandType;

    /**
     * 指令参数 (保留字段, 当前批量操作不支持)
     */
    private Map<String, Object> params;

}