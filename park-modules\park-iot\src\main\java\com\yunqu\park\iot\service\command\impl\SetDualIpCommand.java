package com.yunqu.park.iot.service.command.impl;

import com.yunqu.park.iot.model.command.CommandType;
import com.yunqu.park.iot.model.command.dto.SetDualIpParams;
import com.yunqu.park.iot.service.command.ICommand;
import lombok.RequiredArgsConstructor;

@RequiredArgsConstructor
public class SetDualIpCommand implements ICommand {

    private final SetDualIpParams params;
    private static final String TEMPLATE = "SERVER2,%d,%s,%d,%s,%d#";

    @Override
    public CommandType getType() {
        return CommandType.SET_DUAL_IP;
    }

    @Override
    public String build() {
        return String.format(TEMPLATE, params.getMode(), params.getAddr1(), params.getPort1(), params.getAddr2(), params.getPort2());
    }

    @Override
    public String getDescription() {
        return String.format("%s: mode=%d, server1=%s:%d, server2=%s:%d",
            getType().getDescription(), params.getMode(), params.getAddr1(), params.getPort1(), params.getAddr2(), params.getPort2());
    }
}