package com.yunqu.park.iot.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.yunqu.park.common.mybatis.core.page.PageQuery;
import com.yunqu.park.common.mybatis.core.page.TableDataInfo;
import com.yunqu.park.iot.domain.IotDevice;
import com.yunqu.park.iot.domain.bo.IotDeviceBo;
import com.yunqu.park.iot.domain.vo.IotDeviceVo;

import java.util.Collection;
import java.util.List;

/**
 * IoT设备Service接口
 *
 * <AUTHOR>
 */
public interface IIotDeviceService extends IService<IotDevice> {

    /**
     * 查询IoT设备列表
     */
    TableDataInfo<IotDeviceVo> queryPageList(IotDeviceBo bo, PageQuery pageQuery);

    /**
     * 查询IoT设备列表
     */
    List<IotDeviceVo> queryList(IotDeviceBo bo);

    /**
     * 根据新增业务对象插入IoT设备
     */
    Boolean insertByBo(IotDeviceBo bo);

    /**
     * 根据编辑业务对象修改IoT设备
     */
    Boolean updateByBo(IotDeviceBo bo);

    /**
     * 校验并批量删除IoT设备信息
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    /**
     * 根据IMEI查询设备信息
     * @param imei 设备IMEI号
     * @return 设备信息
     */
    IotDeviceVo queryByImei(String imei);

    /**
     * 根据ID查询设备信息
     * @param id 设备ID
     * @return 设备信息
     */
    IotDeviceVo queryById(Long id);

    /**
     * 根据IMEI查询设备实体
     * @param imei 设备IMEI号
     * @return 设备实体
     */
    IotDevice getDeviceByImei(String imei);

    /**
     * 设备上线处理
     * @param imei 设备IMEI号
     * @param clientIp 客户端IP
     */
    void handleDeviceOnline(String imei, String clientIp);

    /**
     * 设备离线处理
     * @param imei 设备IMEI号
     */
    void handleDeviceOffline(String imei);

    /**
     * 更新设备SIM卡信息
     * @param imei 设备IMEI号
     * @param imsi SIM卡IMSI号
     * @param iccid SIM卡ICCID号
     */
    void updateDeviceSimInfo(String imei, String imsi, String iccid);

    /**
     * 检查设备是否存在
     * @param imei 设备IMEI号
     * @return 是否存在
     */
    boolean existsByImei(String imei);

    /**
     * 获取设备统计信息
     * @return 统计信息
     */
    Object getDeviceStatistics();
}
