package com.yunqu.park.iot.monitor;

import com.yunqu.park.iot.netty.manager.DeviceConnectionManager;
import com.yunqu.park.iot.pool.IotMessagePool;
import com.yunqu.park.iot.utils.ByteBufOptimizer;
import jakarta.annotation.PostConstruct;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.actuate.health.Health;
import org.springframework.boot.actuate.health.HealthIndicator;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.atomic.AtomicLong;

/**
 * IoT指标收集器
 * 阶段4优化：全面的性能监控和健康检查
 *
 * <AUTHOR>
 */
@Slf4j
@Component
@ConditionalOnProperty(prefix = "iot.monitoring", name = "enabled", havingValue = "true", matchIfMissing = true)
public class IotMetricsCollector implements HealthIndicator {

    @Autowired
    private DeviceConnectionManager connectionManager;

    @Autowired(required = false)
    private IotMessagePool messagePool;

    // 消息处理统计
    private final AtomicLong totalMessagesProcessed = new AtomicLong(0);
    private final AtomicLong totalMessagesReceived = new AtomicLong(0);
    private final AtomicLong totalMessagesFailed = new AtomicLong(0);
    private final AtomicLong totalLoginMessages = new AtomicLong(0);
    private final AtomicLong totalHeartbeatMessages = new AtomicLong(0);
    private final AtomicLong totalLocationMessages = new AtomicLong(0);

    // 连接统计
    private final AtomicLong totalConnectionsAccepted = new AtomicLong(0);
    private final AtomicLong totalConnectionsRejected = new AtomicLong(0);
    private final AtomicLong totalConnectionsClosed = new AtomicLong(0);

    // 异常统计
    private final AtomicLong totalNetworkExceptions = new AtomicLong(0);
    private final AtomicLong totalProtocolExceptions = new AtomicLong(0);
    private final AtomicLong totalUnknownExceptions = new AtomicLong(0);

    // 性能统计
    private volatile long lastResetTime = System.currentTimeMillis();

    @PostConstruct
    public void init() {
        log.info("[METRICS-COLLECTOR] ✅ IoT指标收集器初始化完成");
    }

    /**
     * 记录消息接收
     */
    public void recordMessageReceived(byte protocol) {
        totalMessagesReceived.incrementAndGet();

        // 按协议类型分类统计
        switch (protocol) {
            case 0x01 -> totalLoginMessages.incrementAndGet();
            case 0x13 -> totalHeartbeatMessages.incrementAndGet();
            case 0x12 -> totalLocationMessages.incrementAndGet();
        }

        log.debug("[METRICS-COLLECTOR] 记录消息接收: Protocol=0x{}", String.format("%02X", protocol & 0xFF));
    }

    /**
     * 记录消息处理成功
     */
    public void recordMessageProcessed(byte protocol) {
        totalMessagesProcessed.incrementAndGet();
        log.debug("[METRICS-COLLECTOR] 记录消息处理成功: Protocol=0x{}", String.format("%02X", protocol & 0xFF));
    }

    /**
     * 记录消息处理失败
     */
    public void recordMessageFailed(byte protocol, String errorType) {
        totalMessagesFailed.incrementAndGet();
        log.debug("[METRICS-COLLECTOR] 记录消息处理失败: Protocol=0x{}, Error={}",
                 String.format("%02X", protocol & 0xFF), errorType);
    }

    /**
     * 记录连接接受
     */
    public void recordConnectionAccepted(String remoteAddress) {
        totalConnectionsAccepted.incrementAndGet();
        log.debug("[METRICS-COLLECTOR] 记录连接接受: RemoteAddress={}", remoteAddress);
    }

    /**
     * 记录连接拒绝
     */
    public void recordConnectionRejected(String remoteAddress, String reason) {
        totalConnectionsRejected.incrementAndGet();
        log.debug("[METRICS-COLLECTOR] 记录连接拒绝: RemoteAddress={}, Reason={}", remoteAddress, reason);
    }

    /**
     * 记录连接关闭
     */
    public void recordConnectionClosed(String remoteAddress) {
        totalConnectionsClosed.incrementAndGet();
        log.debug("[METRICS-COLLECTOR] 记录连接关闭: RemoteAddress={}", remoteAddress);
    }

    /**
     * 记录网络异常
     */
    public void recordNetworkException(String remoteAddress, String error) {
        totalNetworkExceptions.incrementAndGet();
        log.debug("[METRICS-COLLECTOR] 记录网络异常: RemoteAddress={}, Error={}", remoteAddress, error);
    }

    /**
     * 记录协议异常
     */
    public void recordProtocolException(String remoteAddress, String error) {
        totalProtocolExceptions.incrementAndGet();
        log.debug("[METRICS-COLLECTOR] 记录协议异常: RemoteAddress={}, Error={}", remoteAddress, error);
    }

    /**
     * 记录未知异常
     */
    public void recordUnknownException(String remoteAddress, String error) {
        totalUnknownExceptions.incrementAndGet();
        log.debug("[METRICS-COLLECTOR] 记录未知异常: RemoteAddress={}, Error={}", remoteAddress, error);
    }

    /**
     * 获取所有指标
     */
    public Map<String, Object> getAllMetrics() {
        Map<String, Object> metrics = new HashMap<>();

        // 连接指标
        metrics.put("connections_active", connectionManager.getOnlineDeviceCount());
        metrics.put("connections_accepted_total", totalConnectionsAccepted.get());
        metrics.put("connections_rejected_total", totalConnectionsRejected.get());
        metrics.put("connections_closed_total", totalConnectionsClosed.get());

        // 消息指标
        metrics.put("messages_received_total", totalMessagesReceived.get());
        metrics.put("messages_processed_total", totalMessagesProcessed.get());
        metrics.put("messages_failed_total", totalMessagesFailed.get());
        metrics.put("messages_login_total", totalLoginMessages.get());
        metrics.put("messages_heartbeat_total", totalHeartbeatMessages.get());
        metrics.put("messages_location_total", totalLocationMessages.get());

        // 异常指标
        metrics.put("exceptions_network_total", totalNetworkExceptions.get());
        metrics.put("exceptions_protocol_total", totalProtocolExceptions.get());
        metrics.put("exceptions_unknown_total", totalUnknownExceptions.get());

        // 性能指标
        long uptime = System.currentTimeMillis() - lastResetTime;
        metrics.put("uptime_seconds", uptime / 1000);

        long totalMessages = totalMessagesReceived.get();
        if (totalMessages > 0) {
            double successRate = (double) totalMessagesProcessed.get() / totalMessages * 100;
            metrics.put("message_success_rate_percent", String.format("%.2f", successRate));
        } else {
            metrics.put("message_success_rate_percent", "0.00");
        }

        // 对象池指标
        if (messagePool != null) {
            metrics.put("message_pool_statistics", messagePool.getPoolStatistics());
        }

        // ByteBuf池指标
        metrics.put("bytebuf_pool_statistics", ByteBufOptimizer.getPoolStatistics());

        // 连接管理器指标
        metrics.put("connection_manager_statistics", connectionManager.getConnectionStatistics());

        return metrics;
    }

    /**
     * 重置所有统计
     */
    public void resetStatistics() {
        totalMessagesProcessed.set(0);
        totalMessagesReceived.set(0);
        totalMessagesFailed.set(0);
        totalLoginMessages.set(0);
        totalHeartbeatMessages.set(0);
        totalLocationMessages.set(0);

        totalConnectionsAccepted.set(0);
        totalConnectionsRejected.set(0);
        totalConnectionsClosed.set(0);

        totalNetworkExceptions.set(0);
        totalProtocolExceptions.set(0);
        totalUnknownExceptions.set(0);

        lastResetTime = System.currentTimeMillis();

        log.info("[METRICS-COLLECTOR] 统计数据已重置");
    }

    /**
     * 健康检查实现
     */
    @Override
    public Health health() {
        try {
            Map<String, Object> details = new HashMap<>();

            // 检查连接数
            int activeConnections = connectionManager.getOnlineDeviceCount();
            details.put("active_connections", activeConnections);

            // 检查消息处理成功率
            long totalReceived = totalMessagesReceived.get();
            long totalProcessed = totalMessagesProcessed.get();
            double successRate = totalReceived > 0 ? (double) totalProcessed / totalReceived * 100 : 100.0;
            details.put("message_success_rate", String.format("%.2f%%", successRate));

            // 检查异常率
            long totalExceptions = totalNetworkExceptions.get() + totalProtocolExceptions.get() + totalUnknownExceptions.get();
            double exceptionRate = totalReceived > 0 ? (double) totalExceptions / totalReceived * 100 : 0.0;
            details.put("exception_rate", String.format("%.2f%%", exceptionRate));

            // 健康状态判断
            if (successRate >= 95.0 && exceptionRate <= 5.0) {
                return Health.up()
                        .withDetails(details)
                        .build();
            } else if (successRate >= 80.0 && exceptionRate <= 20.0) {
                return Health.status("WARNING")
                        .withDetails(details)
                        .withDetail("warning", "性能指标接近阈值")
                        .build();
            } else {
                return Health.down()
                        .withDetails(details)
                        .withDetail("error", "性能指标低于阈值")
                        .build();
            }

        } catch (Exception e) {
            return Health.down()
                    .withDetail("error", "健康检查异常: " + e.getMessage())
                    .build();
        }
    }

    /**
     * 获取简要统计信息
     */
    public String getStatisticsSummary() {
        return String.format("连接数: %d, 消息接收: %d, 消息处理: %d, 处理失败: %d, 成功率: %.2f%%",
                connectionManager.getOnlineDeviceCount(),
                totalMessagesReceived.get(),
                totalMessagesProcessed.get(),
                totalMessagesFailed.get(),
                totalMessagesReceived.get() > 0 ?
                    (double) totalMessagesProcessed.get() / totalMessagesReceived.get() * 100 : 0.0);
    }
}
