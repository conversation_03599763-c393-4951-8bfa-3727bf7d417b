package com.yunqu.park.iot.model.dto.command;

import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 设置ACC报警请求
 * 
 * <AUTHOR>
 * @since 2025-01-06
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class SetAccAlarmRequest extends BaseCommandReq {
    
    /**
     * 是否启用ACC报警
     * true: 启用, false: 禁用
     */
    @NotNull(message = "ACC报警开关不能为空")
    private Boolean enabled;
}