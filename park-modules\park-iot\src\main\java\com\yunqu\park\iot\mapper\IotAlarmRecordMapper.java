package com.yunqu.park.iot.mapper;

import com.yunqu.park.common.mybatis.core.mapper.BaseMapperPlus;
import com.yunqu.park.iot.domain.IotAlarmRecord;
import com.yunqu.park.iot.domain.vo.IotAlarmRecordVo;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

/**
 * 设备报警记录Mapper接口
 *
 * <AUTHOR>
 */
public interface IotAlarmRecordMapper extends BaseMapperPlus<IotAlarmRecord, IotAlarmRecordVo> {

    /**
     * 根据设备IMEI查询未处理报警
     * @param imei 设备IMEI号
     * @return 未处理报警列表
     */
    List<IotAlarmRecordVo> selectUnhandledAlarmsByImei(@Param("imei") String imei);

    /**
     * 更新报警处理状态
     * @param alarmId 报警记录ID
     * @param handleUser 处理人
     * @param handleRemark 处理备注
     */
    void updateAlarmHandleStatus(@Param("alarmId") Long alarmId,
                                @Param("handleUser") Long handleUser,
                                @Param("handleRemark") String handleRemark);

    /**
     * 根据报警类型统计报警数量
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 统计结果
     */
    Object selectAlarmStatistics(@Param("startTime") Date startTime,
                                @Param("endTime") Date endTime);

    /**
     * 根据设备ID和时间范围查询报警记录
     * @param deviceId 设备ID
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 报警记录列表
     */
    List<IotAlarmRecordVo> selectAlarmsByDeviceAndTime(@Param("deviceId") Long deviceId,
                                                      @Param("startTime") Date startTime,
                                                      @Param("endTime") Date endTime);
}
