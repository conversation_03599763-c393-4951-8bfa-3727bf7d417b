package com.yunqu.park.iot.model.command.dto;

import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 通用驾驶行为报警设置参数.
 * 适用于: 急加速报警, 急减速报警, 急转弯报警, 碰撞报警.
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class SetDrivingBehaviorParams extends CommandParams {
    @NotNull(message = "报警阈值不能为空")
    private Integer value;

    @NotNull(message = "报警模式不能为空")
    private Integer mode;
}