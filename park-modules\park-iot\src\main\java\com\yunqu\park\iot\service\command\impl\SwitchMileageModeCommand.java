package com.yunqu.park.iot.service.command.impl;

import com.yunqu.park.iot.model.command.CommandType;
import com.yunqu.park.iot.model.command.dto.SwitchMileageModeParams;
import com.yunqu.park.iot.service.command.ICommand;
import lombok.RequiredArgsConstructor;

@RequiredArgsConstructor
public class SwitchMileageModeCommand implements ICommand {

    private final SwitchMileageModeParams params;
    private static final String TEMPLATE = "ODOMODE,%d#";

    @Override
    public CommandType getType() {
        return CommandType.SWITCH_MILEAGE_MODE;
    }

    @Override
    public String build() {
        return String.format(TEMPLATE, params.getMode());
    }

    @Override
    public String getDescription() {
        return String.format("%s: 模式=%d", getType().getDescription(), params.getMode());
    }
}