package com.yunqu.park.iot.model.command.dto;

import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = false)
public class SetIpParams extends CommandParams {
    @NotNull(message = "模式不能为空")
    @Min(value = 0, message = "模式必须为0或1")
    @Max(value = 1, message = "模式必须为0或1")
    private Integer mode;

    @NotBlank(message = "服务器地址不能为空")
    private String address;

    @NotNull(message = "端口不能为空")
    private Integer port;
}