package com.yunqu.park.iot.model.dto.command;

import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.Max;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 设置GPS模式请求
 * 
 * <AUTHOR>
 * @since 2025-01-06
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class SetGpsModeRequest extends BaseCommandReq {
    
    /**
     * GPS模式
     * 0: 省电模式, 1: 正常模式, 2: 高精度模式
     */
    @NotNull(message = "GPS模式不能为空")
    @Min(value = 0, message = "GPS模式值不能小于0")
    @Max(value = 2, message = "GPS模式值不能大于2")
    private Integer mode;
}