package com.yunqu.park.iot.model.dto.command;

import com.yunqu.park.iot.model.command.dto.SetUploadIntervalParams;
import lombok.Data;
import lombok.EqualsAndHashCode;

import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;

/**
 * 定时上传间隔指令请求
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class SetUploadIntervalRequest extends BaseCommandReq {

    /**
     * 上传间隔设置参数
     */
    @Valid
    @NotNull(message = "上传间隔参数不能为空")
    private SetUploadIntervalParams params;
}