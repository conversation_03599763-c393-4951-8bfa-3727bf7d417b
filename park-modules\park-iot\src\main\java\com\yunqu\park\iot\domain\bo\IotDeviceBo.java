package com.yunqu.park.iot.domain.bo;

import com.yunqu.park.common.core.validate.AddGroup;
import com.yunqu.park.common.core.validate.EditGroup;
import com.yunqu.park.common.mybatis.core.domain.BaseEntity;
import com.yunqu.park.iot.domain.IotDevice;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.EqualsAndHashCode;

import jakarta.validation.constraints.*;
import java.util.Date;

/**
 * IoT设备基础信息业务对象 iot_device
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = IotDevice.class, reverseConvertGenerate = false)
public class IotDeviceBo extends BaseEntity {

    /**
     * 设备ID
     */
    @NotNull(message = "设备ID不能为空", groups = { EditGroup.class })
    private Long deviceId;

    /**
     * 设备IMEI号
     */
    @NotBlank(message = "设备IMEI号不能为空", groups = { AddGroup.class, EditGroup.class })
    @Size(min = 15, max = 15, message = "IMEI号必须为15位")
    private String imei;

    /**
     * 设备名称
     */
    @Size(max = 100, message = "设备名称不能超过100个字符")
    private String deviceName;

    /**
     * 设备类型
     */
    private String deviceType;

    /**
     * SIM卡IMSI号
     */
    @Size(max = 15, message = "IMSI号不能超过15位")
    private String simImsi;

    /**
     * SIM卡ICCID号
     */
    @Size(max = 20, message = "ICCID号不能超过20位")
    private String simIccid;

    /**
     * 设备状态:0-离线,1-在线,2-休眠
     */
    private String status;

    /**
     * 最后在线时间
     */
    private Date lastOnlineTime;

    /**
     * 注册时间
     */
    private Date registerTime;

    /**
     * 备注
     */
    private String remark;

}
