package com.yunqu.park.iot.service;

import com.yunqu.park.iot.model.command.CommandRequest;
import com.yunqu.park.iot.model.command.CommandType;
import com.yunqu.park.iot.model.command.dto.CommandParams;
import com.yunqu.park.iot.model.dto.BatchCommandRequest;
import com.yunqu.park.iot.model.dto.command.*;

/**
 * IoT设备指令服务接口 (V2.0 - 重构版)
 *
 * <p>定义了IoT设备指令下发的核心业务逻辑接口，支持54种不同类型的设备指令。
 * 该接口采用分层设计，提供统一的指令执行框架和类型安全的指令方法。</p>
 *
 * <h3>接口层次结构：</h3>
 * <ul>
 *   <li><strong>核心执行层</strong>：executeCommand() - 底层指令执行逻辑</li>
 *   <li><strong>统一接口层</strong>：sendCommand() - 通用指令发送接口</li>
 *   <li><strong>类型安全层</strong>：具体指令方法 - 为每个指令类型提供专门方法</li>
 *   <li><strong>兼容性层</strong>：@Deprecated方法 - 保持向后兼容性</li>
 * </ul>
 *
 * <h3>指令执行流程：</h3>
 * <ol>
 *   <li><strong>参数验证</strong>：验证设备ID和指令参数的有效性</li>
 *   <li><strong>设备检查</strong>：确认设备在线状态</li>
 *   <li><strong>指令构建</strong>：使用CommandFactory创建具体指令对象</li>
 *   <li><strong>指令发送</strong>：通过Netty连接管理器发送到设备</li>
 *   <li><strong>结果记录</strong>：记录指令历史和缓存执行结果</li>
 * </ol>
 *
 * <h3>支持的指令类型：</h3>
 * <ul>
 *   <li><strong>基础指令</strong>：重启(reboot)、恢复出厂(factoryReset)、IP设置等</li>
 *   <li><strong>查询指令</strong>：位置查询(queryLocation)、状态查询(queryStatus)等</li>
 *   <li><strong>报警指令</strong>：震动报警、超速报警、低电报警等</li>
 *   <li><strong>功能指令</strong>：GPS模式设置、里程统计、休眠配置等</li>
 * </ul>
 *
 * <h3>设计特点：</h3>
 * <ul>
 *   <li><strong>类型安全</strong>：每个指令方法使用专门的Request DTO</li>
 *   <li><strong>异常处理</strong>：统一的异常处理和错误日志记录</li>
 *   <li><strong>缓存支持</strong>：指令执行结果缓存和历史记录</li>
 *   <li><strong>批量操作</strong>：支持向多个设备发送相同指令</li>
 * </ul>
 *
 * <h3>实现要求：</h3>
 * <ul>
 *   <li>所有指令方法必须进行设备在线状态检查</li>
 *   <li>必须记录指令执行历史和结果</li>
 *   <li>必须提供详细的日志记录用于问题排查</li>
 *   <li>必须处理网络异常和设备离线情况</li>
 * </ul>
 *
 * <AUTHOR>
 * <AUTHOR> (重构)
 * @version 2.0
 * @since 2025-01-06
 * @see IotCommandServiceImpl
 * @see com.yunqu.park.iot.service.command.CommandFactory
 * @see com.yunqu.park.iot.model.command.CommandType
 */
public interface IIotCommandService {

    /**
     * 执行指令 (新版统一接口)
     *
     * @param request 包含设备IMEI、指令类型和参数的请求对象
     */
    void sendCommand(CommandRequest request);

    /**
     * 执行指令 (旧版核心逻辑，可设为内部使用)
     *
     * @param imei   设备IMEI
     * @param type   指令类型
     * @param params 指令参数
     * @return 是否成功
     */
    Boolean executeCommand(String imei, CommandType type, CommandParams params);

    /**
     * 发送重启指令
     *
     * @param imei 设备IMEI
     * @return 操作结果
     */
    @Deprecated
    Boolean sendRestartCommand(String imei);

    /**
     * 发送恢复出厂设置指令
     *
     * @param imei 设备IMEI
     * @return 操作结果
     */
    @Deprecated
    Boolean sendResetCommand(String imei);

    /**
     * 设置心跳上报间隔
     *
     * @param imei     设备IMEI
     * @param interval 间隔时间（秒）
     * @return 操作结果
     */
    @Deprecated
    Boolean setReportInterval(String imei, Integer interval);

    /**
     * 设置APN
     *
     * @param imei     设备IMEI
     * @param apn      APN名称
     * @param username 用户名
     * @param password 密码
     * @return 操作结果
     */
    @Deprecated
    Boolean setApnConfig(String imei, String apn, String username, String password);

    /**
     * 设置服务器地址
     *
     * @param imei     设备IMEI
     * @param serverIp 服务器IP
     * @param port     端口
     * @return 操作结果
     */
    @Deprecated
    Boolean setServerAddress(String imei, String serverIp, Integer port);

    /**
     * 查询设备状态
     *
     * @param imei 设备IMEI
     * @return 操作结果
     */
    @Deprecated
    Boolean queryDeviceStatus(String imei);

    /**
     * 设置时区
     *
     * @param imei     设备IMEI
     * @param timezone 时区
     * @return 操作结果
     */
    @Deprecated
    Boolean setTimezone(String imei, Integer timezone);

    /**
     * 设置工作模式
     *
     * @param imei 设备IMEI
     * @param mode 模式
     * @return 操作结果
     */
    @Deprecated
    Boolean setWorkMode(String imei, Integer mode);

    /**
     * 发送自定义指令
     *
     * @param imei    设备IMEI
     * @param command 指令内容
     * @return 操作结果
     */
    @Deprecated
    Boolean sendCustomCommand(String imei, String command);

    /**
     * 获取指令历史记录
     *
     * @param imei  设备IMEI
     * @param limit 记录条数
     * @return 历史记录列表
     */
    Object getCommandHistory(String imei, Integer limit);

    /**
     * 批量发送指令
     *
     * @param request 批量请求对象
     * @return 结果摘要
     */
    Object sendBatchCommand(BatchCommandRequest request);

    // ========== 新的独立指令接口 ==========

    /**
     * 发送重启指令 (新)
     *
     * @param request 重启请求
     */
    void reboot(RebootRequest request);

    /**
     * 设置IP地址 (新)
     *
     * @param request IP设置请求
     */
    void setIp(SetIpRequest request);

    /**
     * 查询设备位置 (新)
     *
     * @param request 查询位置请求
     */
    void queryLocation(QueryLocationRequest request);

    /**
     * 恢复出厂设置 (新)
     *
     * @param request 恢复出厂设置请求
     */
    void factoryReset(FactoryResetRequest request);

    /**
     * 查询链路地址 (新)
     *
     * @param request 查询链路地址请求
     */
    void queryLinkAddress(QueryLinkAddressRequest request);

    /**
     * 查询参数 (新)
     *
     * @param request 查询参数请求
     */
    void queryParameters(QueryParametersRequest request);

    /**
     * 查询状态 (新)
     *
     * @param request 查询状态请求
     */
    void queryStatus(QueryStatusRequest request);

    /**
     * 查询版本 (新)
     *
     * @param request 查询版本请求
     */
    void queryVersion(QueryVersionRequest request);

    /**
     * 查询ICCID (新)
     *
     * @param request 查询ICCID请求
     */
    void queryIccid(QueryIccidRequest request);

    /**
     * 查询网络 (新)
     *
     * @param request 查询网络请求
     */
    void queryNetwork(QueryNetworkRequest request);

    /**
     * 查询模块版本 (新)
     *
     * @param request 查询模块版本请求
     */
    void queryModuleVersion(QueryModuleVersionRequest request);

    /**
     * 查询震动报警灵敏度 (新)
     *
     * @param request 查询震动报警灵敏度请求
     */
    void queryVibrationSensitivity(QueryVibrationSensitivityRequest request);

    /**
     * 里程统计查询 (新)
     *
     * @param request 里程统计查询请求
     */
    void queryMileageStatistics(QueryMileageStatisticsRequest request);

    /**
     * 查询网络模式 (新)
     *
     * @param request 查询网络模式请求
     */
    void queryNetworkMode(QueryNetworkModeRequest request);

    /**
     * 里程清零 (新)
     *
     * @param request 里程清零请求
     */
    void clearMileage(ClearMileageRequest request);

    /**
     * 删除中心号码 (新)
     *
     * @param request 删除中心号码请求
     */
    void deleteCenterNumber(DeleteCenterNumberRequest request);

    /**
     * 撤防后设备状态 (新)
     *
     * @param request 撤防后设备状态请求
     */
    void setDisarmState(SetDisarmStateRequest request);

    /**
     * 清除各种数据 (新)
     *
     * @param request 清除各种数据请求
     */
    void clearData(ClearDataRequest request);

    /**
     * 设置双IP (新)
     *
     * @param request 设置双IP请求
     */
    void setDualIp(SetDualIpRequest request);

    /**
     * 设置APN (新)
     *
     * @param request 设置APN请求
     */
    void setApn(SetApnRequest request);

    /**
     * 定时上传间隔 (新)
     *
     * @param request 定时上传间隔请求
     */
    void setUploadInterval(SetUploadIntervalRequest request);

    /**
     * 设置心跳间隔 (新)
     *
     * @param request 设置心跳间隔请求
     */
    void setHeartbeatInterval(SetHeartbeatIntervalRequest request);

    /**
     * 设置SOS号码 (新)
     *
     * @param request 设置SOS号码请求
     */
    void setSosNumber(SetSosNumberRequest request);

    /**
     * 删除SOS号码 (新)
     *
     * @param request 删除SOS号码请求
     */
    void deleteSosNumber(DeleteSosNumberRequest request);

    /**
     * 设置中心号码 (新)
     *
     * @param request 设置中心号码请求
     */
    void setCenterNumber(SetCenterNumberRequest request);

    /**
     * 控制继电器 (新)
     *
     * @param request 控制继电器请求
     */
    void controlRelay(ControlRelayRequest request);

    /**
     * 设置时区 (新)
     *
     * @param request 设置时区请求
     */
    void setTimezone(SetTimezoneRequest request);

    /**
     * 设置语言 (新)
     *
     * @param request 设置语言请求
     */
    void setLanguage(SetLanguageRequest request);

    /**
     * 设置自动布防 (新)
     *
     * @param request 设置自动布防请求
     */
    void setAutoArm(SetAutoArmRequest request);

    /**
     * 设置角度上传 (新)
     *
     * @param request 设置角度上传请求
     */
    void setAngleUpload(SetAngleUploadRequest request);

    /**
     * 设置角度值 (新)
     *
     * @param request 设置角度值请求
     */
    void setAngleValue(SetAngleValueRequest request);

    /**
     * 设置上传时区 (新)
     *
     * @param request 设置上传时区请求
     */
    void setUploadTimezone(SetUploadTimezoneRequest request);

    /**
     * 设置震动灵敏度 (新)
     *
     * @param request 设置震动灵敏度请求
     */
    void setVibrationSensitivity(SetVibrationSensitivityRequest request);

    /**
     * 设置震动报警 (新)
     *
     * @param request 设置震动报警请求
     */
    void setVibrationAlarm(SetVibrationAlarmRequest request);

    /**
     * 设置断电报警 (新)
     *
     * @param request 设置断电报警请求
     */
    void setPowerAlarm(SetPowerAlarmRequest request);

    /**
     * 设置低电报警 (新)
     *
     * @param request 设置低电报警请求
     */
    void setLowBatteryAlarm(SetLowBatteryAlarmRequest request);

    /**
     * 设置超速报警 (新)
     *
     * @param request 设置超速报警请求
     */
    void setSpeedAlarm(SetSpeedAlarmRequest request);

    /**
     * 设置移动报警 (新)
     *
     * @param request 设置移动报警请求
     */
    void setMovingAlarm(SetMovingAlarmRequest request);

    /**
     * 设置ACC报警 (新)
     *
     * @param request 设置ACC报警请求
     */
    void setAccAlarm(SetAccAlarmRequest request);

    /**
     * 设置急加速报警 (新)
     *
     * @param request 设置急加速报警请求
     */
    void setHarshAccelerationAlarm(SetHarshAccelerationAlarmRequest request);

    /**
     * 设置急刹车报警 (新)
     *
     * @param request 设置急刹车报警请求
     */
    void setHarshBrakingAlarm(SetHarshBrakingAlarmRequest request);

    /**
     * 设置急转弯报警 (新)
     *
     * @param request 设置急转弯报警请求
     */
    void setHarshTurningAlarm(SetHarshTurningAlarmRequest request);

    /**
     * 设置碰撞报警 (新)
     *
     * @param request 设置碰撞报警请求
     */
    void setCollisionAlarm(SetCollisionAlarmRequest request);

    /**
     * 切换里程模式 (新)
     *
     * @param request 切换里程模式请求
     */
    void switchMileageMode(SwitchMileageModeRequest request);

    /**
     * 设置里程统计 (新)
     *
     * @param request 设置里程统计请求
     */
    void setMileageStatistics(SetMileageStatisticsRequest request);

    /**
     * 设置静止休眠 (新)
     *
     * @param request 设置静止休眠请求
     */
    void setStaticSleep(SetStaticSleepRequest request);

    /**
     * 设置测速模式 (新)
     *
     * @param request 设置测速模式请求
     */
    void setSpeedMeasurementMode(SetSpeedMeasurementModeRequest request);

    /**
     * 设置GPS模式 (新)
     *
     * @param request 设置GPS模式请求
     */
    void setGpsMode(SetGpsModeRequest request);

    /**
     * 设置GPS上传时长 (新)
     *
     * @param request 设置GPS上传时长请求
     */
    void setGpsUploadDuration(SetGpsUploadDurationRequest request);

    /**
     * 设置GPRS开关 (新)
     *
     * @param request 设置GPRS开关请求
     */
    void setGprsSwitch(SetGprsSwitchRequest request);

    /**
     * 设置卫星锁定开关 (新)
     *
     * @param request 设置卫星锁定开关请求
     */
    void setSatelliteLockSwitch(SetSatelliteLockSwitchRequest request);

    /**
     * 设置距离上传 (新)
     *
     * @param request 设置距离上传请求
     */
    void setDistanceUpload(SetDistanceUploadRequest request);

    /**
     * 设置GPS休眠工作 (新)
     *
     * @param request 设置GPS休眠工作请求
     */
    void setGpsSleepWork(SetGpsSleepWorkRequest request);

    /**
     * 设置静止上传间隔 (新)
     *
     * @param request 设置静止上传间隔请求
     */
    void setStaticUploadInterval(SetStaticUploadIntervalRequest request);
}
