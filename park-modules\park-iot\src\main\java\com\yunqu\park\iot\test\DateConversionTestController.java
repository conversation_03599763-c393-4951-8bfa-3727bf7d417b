package com.yunqu.park.iot.test;

import com.yunqu.park.common.core.domain.R;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.sql.Timestamp;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * 日期转换测试控制器
 * 用于测试全局Date类型转换功能
 *
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/iot/test/date")
public class DateConversionTestController {

    /**
     * 测试Date类型转换 - GET请求参数
     */
    @GetMapping("/test-params")
    public R<Map<String, Object>> testDateParams(
            @RequestParam(required = false) Date createTime,
            @RequestParam(required = false) Date updateTime,
            @RequestParam(required = false) java.sql.Date sqlDate,
            @RequestParam(required = false) Timestamp timestamp) {
        
        log.info("Received date parameters: createTime={}, updateTime={}, sqlDate={}, timestamp={}", 
                createTime, updateTime, sqlDate, timestamp);
        
        Map<String, Object> result = new HashMap<>();
        result.put("createTime", createTime);
        result.put("updateTime", updateTime);
        result.put("sqlDate", sqlDate);
        result.put("timestamp", timestamp);
        result.put("message", "Date parameters converted successfully");
        
        return R.ok(result);
    }

    /**
     * 测试Date类型转换 - POST请求体
     */
    @PostMapping("/test-body")
    public R<TestDateEntity> testDateBody(@RequestBody TestDateEntity entity) {
        log.info("Received date entity: {}", entity);
        return R.ok(entity);
    }

    /**
     * 测试各种Date格式
     */
    @PostMapping("/test-formats")
    public R<Map<String, Object>> testDateFormats(@RequestBody Map<String, String> dateStrings) {
        Map<String, Object> result = new HashMap<>();
        
        for (Map.Entry<String, String> entry : dateStrings.entrySet()) {
            String key = entry.getKey();
            String value = entry.getValue();
            
            try {
                // 这里会触发String到Date的转换
                TestDateEntity entity = new TestDateEntity();
                entity.setCreateTime(parseDate(value));
                
                result.put(key + "_original", value);
                result.put(key + "_converted", entity.getCreateTime());
                result.put(key + "_success", true);
                
            } catch (Exception e) {
                result.put(key + "_original", value);
                result.put(key + "_error", e.getMessage());
                result.put(key + "_success", false);
            }
        }
        
        return R.ok(result);
    }

    /**
     * 测试空值和特殊值处理
     */
    @PostMapping("/test-empty-values")
    public R<Map<String, Object>> testEmptyValues() {
        Map<String, Object> result = new HashMap<>();
        
        // 测试各种空值情况
        String[] testValues = {"", " ", "null", "undefined", null};
        
        for (int i = 0; i < testValues.length; i++) {
            String value = testValues[i];
            try {
                TestDateEntity entity = new TestDateEntity();
                entity.setCreateTime(parseDate(value));
                
                result.put("test_" + i + "_value", value);
                result.put("test_" + i + "_result", entity.getCreateTime());
                result.put("test_" + i + "_success", true);
                
            } catch (Exception e) {
                result.put("test_" + i + "_value", value);
                result.put("test_" + i + "_error", e.getMessage());
                result.put("test_" + i + "_success", false);
            }
        }
        
        return R.ok(result);
    }

    /**
     * 简单的日期解析方法（用于测试）
     */
    private Date parseDate(String dateString) {
        if (dateString == null || dateString.trim().isEmpty()) {
            return null;
        }
        // 这里实际会使用全局转换器
        return new Date();
    }

    /**
     * 测试用的Date实体类
     */
    @Data
    public static class TestDateEntity {
        private Date createTime;
        private Date updateTime;
        private Date lastOnlineTime;
        private Date registerTime;
        private java.sql.Date sqlDate;
        private Timestamp timestamp;
        private String description;
    }
}
