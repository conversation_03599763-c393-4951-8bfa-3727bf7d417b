package com.yunqu.park.iot.model.dto.command;

import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.Max;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 设置超速报警请求
 * 
 * <AUTHOR>
 * @since 2025-01-06
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class SetSpeedAlarmRequest extends BaseCommandReq {
    
    /**
     * 是否启用超速报警
     * true: 启用, false: 禁用
     */
    @NotNull(message = "超速报警开关不能为空")
    private Boolean enabled;
    
    /**
     * 超速报警阈值 (km/h: 0-255)
     */
    @Min(value = 0, message = "超速阈值不能小于0")
    @Max(value = 255, message = "超速阈值不能大于255")
    private Integer speedLimit;
}