const parser = require('./index');
//Login
// console.log(parser('78781101035873905215859020203201001f49170d0a'));
//Status
// console.log(parser('787811010868120148273193700332010167b6e30D0A'));
// console.log(parser('78781910130219141d2ccb030f9c30084a61b00014600002009196e90d0a'));
// console.log(parser('79790020940a035354909005443704044509841133878991450900984113387200032b580d0a'))
// console.log(parser('78780B23C0019F040001000818720D0A'));


// console.log(parser("79790098210000000001426174746572793a342e3030562c4e4f524d414c3b20475052533a4c696e6b2055702047534d205369676e616c204c6576656c3a5374726f6e673b204750533a536561726368696e6720736174656c6c6974653b20535653205573656420696e206669783a30283132293b202c20475053205369676e616c204c6576656c3a2020446566656e73653a4f46463b20001d1ae80d0a"));
// console.log(parser("7878110103535490909116777003212100401e820d0a"))
// console.log(parser(`7878 1101 0353 5490 9091 16777003 2121 0027 093b 0d0a`.replace(/\s/g, '')))
// console.log(parser("7878110103535490902431547003212100d3e7e10d0a"))
// console.log(parser("787825266A03586880000001580001100A0C1E0A2E05027AC8390C4657C5000156001DF1000000060D0A"));
	// 7878 0501 0027 9de8 0d0a
// console.log(parser("7878110103535490902518927003212100015ca60d0a"))
// console.log(parser("78782222130c12102c08ce01e07642086bf5a00014120194314f2400e12b000000005095c00d0a"))
// console.log(parser("79790020940a035354909024132304043104938365698991310900493836569900088b550d0a"));
// console.log(parser("7979009e9404414c4d313d44353b414c4d323d44353b414c4d333d35373b535441313d34313b4459443d30313b534f533d2c2c3b43454e5445523d3b46454e43453d46656e63652c4f46462c302c302e3030303030302c302e3030303030302c3330302c494e206f72204f55542c313b49434349443d38393931333130393030343933383336353639393b4d4f44453d4d4f44452c312c31302c3138303b000a98370d0a"))

// console.log(parser('78782627100419092D07C5027AC91C0C4658000005370900000000000000008002000C01FF00004DF60D0A'));

// console.log(parser(`79 79 00 BF 21 00 00 00 00 01 42 61 74 74 65 72 79 3A 32 2E 39 37 56 2C 4E 4F 52 4D 41 4C 3B 20 47 50
// 52 53 3A 4C 69 6E 6B 20 55 70 20 47 53 4D 20 53 69 67 6E 61 6C 20 4C 65 76 65 6C 3A 53 74 72 6F 6E 67 3B 20 47
// 50 53 3A 53 75 63 63 65 73 73 66 75 6C 20 70 6F 73 69 74 69 6F 6E 69 6E 67 3B 20 53 56 53 20 55 73 65 64 20 69 6E
// 20 66 69 78 3A 31 32 28 31 32 29 3B 20 2C 20 47 50 53 20 53 69 67 6E 61 6C 20 4C 65 76 65 6C 3A 33 31 2C 33 31
// 2C 32 37 2C 33 33 2C 33 38 2C 33 31 2C 33 30 2C 33 36 2C 33 34 2C 32 34 2C 33 31 2C 31 38 20 20 44 65 66 65 6E
// 73 65 3A 4F 46 46 3B 20 00 31 90 52 0D 0A`.replace(/\s/g, '')))

// console.log(parser('78782222190109022901c30166d5d00855d6500014f501945679e300a691000e00027a0b5d0d0a'));



// console.log(parser('7878722C130A1708390401942D6219005FDE2562190017862462190003192262190017871C621900A6C31A000000000000000000000000FF08989C57810C2936802689033CD942C4E98445D20D47F2D5BFFBC76E49D8C771053191496466B35F54044A989C5780AF254B60146611CEF651000BE7440D0A'))
// console.log(parser('78781F120F0C1D0B0F34C6027AC74C0C4658100014D401CC00287D001F71002623090D0A'))
// console.log(parser('78781F120B081D112E10CF027AC7EB0C46584900148F01CC00287D001FB800000000000380810D0A'))
// console.log(parser('7878262211031E082228C9026C19000C38D12000153F01CC002866000EEE0000010000000000395C7E0D0A'))

// console.log(parser("78781510140c0e08041e9e016323990853fb390435260381000d0a"));
console.log(parser("7878 12 10 0A03170F3217 9C 026B3F3E 0C22AD65 1F 3460 0D0A".replace(/\s/g, '')));
console.log(parser("7878 06 13 55 23 08 03 0D0A".replace(/\s/g, '')));