package com.yunqu.park.iot.service.command.impl;

import com.yunqu.park.iot.model.command.CommandType;
import com.yunqu.park.iot.model.command.dto.SetIpParams;
import com.yunqu.park.iot.service.command.ICommand;
import lombok.RequiredArgsConstructor;

/**
 * 设置设备IP地址指令实现
 *
 * <p>实现设备服务器IP地址和端口配置功能，用于设置设备连接的目标服务器信息。
 * 该指令支持主服务器和备用服务器的配置，确保设备能够正确连接到指定的服务器。</p>
 *
 * <h3>指令参数：</h3>
 * <ul>
 *   <li><strong>mode</strong>：服务器模式（1=主服务器，2=备用服务器）</li>
 *   <li><strong>address</strong>：服务器IP地址或域名</li>
 *   <li><strong>port</strong>：服务器端口号（1-65535）</li>
 * </ul>
 *
 * <h3>使用场景：</h3>
 * <ul>
 *   <li>设备初始化配置服务器连接信息</li>
 *   <li>服务器迁移时更新设备连接地址</li>
 *   <li>配置主备服务器实现高可用性</li>
 *   <li>网络环境变更时调整连接参数</li>
 * </ul>
 *
 * <h3>配置生效：</h3>
 * <ul>
 *   <li>指令发送成功后，设备会保存新的服务器配置</li>
 *   <li>配置立即生效，设备会尝试连接新的服务器地址</li>
 *   <li>如果新地址无法连接，设备会回退到原有配置</li>
 *   <li>建议在网络稳定时进行IP配置更改</li>
 * </ul>
 *
 * <h3>注意事项：</h3>
 * <ul>
 *   <li>确保新的IP地址和端口可达，避免设备失联</li>
 *   <li>域名解析依赖设备的DNS配置</li>
 *   <li>端口号必须在有效范围内（1-65535）</li>
 *   <li>配置错误可能导致设备无法正常通信</li>
 * </ul>
 *
 * <h3>协议格式：</h3>
 * <pre>{@code
 * 发送: "SERVER,1,*************,8080#"
 * 参数: mode=1, address=*************, port=8080
 * 响应: 设备确认并应用新的服务器配置
 * }</pre>
 *
 * <AUTHOR>
 * @version 2.0
 * @since 2025-01-06
 * @see SetIpParams
 * @see com.yunqu.park.iot.model.command.CommandType#SET_IP
 */
@RequiredArgsConstructor
public class SetIpCommand implements ICommand {

    /** 指令参数对象 */
    private final SetIpParams params;

    /** 设置IP指令模板：SERVER,模式,地址,端口# */
    private static final String TEMPLATE = "SERVER,%d,%s,%d#";

    @Override
    public CommandType getType() {
        return CommandType.SET_IP;
    }

    @Override
    public String build() {
        return String.format(TEMPLATE, params.getMode(), params.getAddress(), params.getPort());
    }

    @Override
    public String getDescription() {
        return String.format("%s: mode=%d, address=%s, port=%d",
            getType().getDescription(), params.getMode(), params.getAddress(), params.getPort());
    }
}