package com.yunqu.park.iot.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.yunqu.park.common.tenant.core.TenantEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;
import java.util.Date;

/**
 * IoT设备基础信息对象 iot_device
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("iot_device")
public class IotDevice extends TenantEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 设备ID
     */
    @TableId(value = "device_id")
    private Long deviceId;

    /**
     * 设备IMEI号
     */
    private String imei;

    /**
     * 设备名称
     */
    private String deviceName;

    /**
     * 设备类型
     */
    private String deviceType;

    /**
     * SIM卡IMSI号
     */
    private String simImsi;

    /**
     * SIM卡ICCID号
     */
    private String simIccid;

    /**
     * 设备状态:0-离线,1-在线,2-休眠
     */
    private String status;

    /**
     * 最后在线时间
     */
    private Date lastOnlineTime;

    /**
     * 注册时间
     */
    private Date registerTime;

    /**
     * 备注
     */
    private String remark;

    /**
     * 删除标志(0代表存在 1代表删除)
     */
    @TableLogic
    private String delFlag;

}
