package com.yunqu.park.iot.model.dto.command;

import com.yunqu.park.iot.model.command.dto.SetVibrationSensitivityParams;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 设置震动灵敏度指令请求
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "设置震动灵敏度指令请求")
public class SetVibrationSensitivityRequest extends BaseCommandReq {

    @Schema(description = "震动灵敏度参数")
    private SetVibrationSensitivityParams params;
}