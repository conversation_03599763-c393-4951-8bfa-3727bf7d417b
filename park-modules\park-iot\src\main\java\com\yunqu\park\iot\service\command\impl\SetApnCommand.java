package com.yunqu.park.iot.service.command.impl;

import com.yunqu.park.iot.model.command.CommandType;
import com.yunqu.park.iot.model.command.dto.SetApnParams;
import com.yunqu.park.iot.service.command.ICommand;
import lombok.RequiredArgsConstructor;
import org.springframework.util.StringUtils;

@RequiredArgsConstructor
public class SetApnCommand implements ICommand {

    private final SetApnParams params;
    private static final String TEMPLATE = "APN,%s,%s,%s#";

    @Override
    public CommandType getType() {
        return CommandType.SET_APN;
    }

    @Override
    public String build() {
        String user = StringUtils.hasText(params.getUser()) ? params.getUser() : "";
        String pass = StringUtils.hasText(params.getPass()) ? params.getPass() : "";
        return String.format(TEMPLATE, params.getApn(), user, pass);
    }

    @Override
    public String getDescription() {
        return String.format("%s: apn=%s", getType().getDescription(), params.getApn());
    }
}