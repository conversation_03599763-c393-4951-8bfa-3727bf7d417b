package com.yunqu.park.iot.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.yunqu.park.common.excel.annotation.ExcelDictFormat;
import com.yunqu.park.common.excel.convert.ExcelDictConvert;
import com.yunqu.park.iot.domain.IotDevice;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;

/**
 * IoT设备基础信息视图对象 iot_device
 *
 * <AUTHOR>
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = IotDevice.class)
public class IotDeviceVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 设备ID
     */
    @ExcelProperty(value = "设备ID")
    private Long deviceId;

    /**
     * 设备IMEI号
     */
    @ExcelProperty(value = "设备IMEI号")
    private String imei;

    /**
     * 设备名称
     */
    @ExcelProperty(value = "设备名称")
    private String deviceName;

    /**
     * 设备类型
     */
    @ExcelProperty(value = "设备类型")
    private String deviceType;

    /**
     * SIM卡IMSI号
     */
    @ExcelProperty(value = "SIM卡IMSI号")
    private String simImsi;

    /**
     * SIM卡ICCID号
     */
    @ExcelProperty(value = "SIM卡ICCID号")
    private String simIccid;

    /**
     * 设备状态:0-离线,1-在线,2-休眠
     */
    @ExcelProperty(value = "设备状态", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "iot_device_status")
    private String status;

    /**
     * 最后在线时间
     */
    @ExcelProperty(value = "最后在线时间")
    private Date lastOnlineTime;

    /**
     * 注册时间
     */
    @ExcelProperty(value = "注册时间")
    private Date registerTime;

    /**
     * 备注
     */
    @ExcelProperty(value = "备注")
    private String remark;

    /**
     * 租户编号
     */
    private String tenantId;

    /**
     * 创建时间
     */
    private Date createTime;

}
