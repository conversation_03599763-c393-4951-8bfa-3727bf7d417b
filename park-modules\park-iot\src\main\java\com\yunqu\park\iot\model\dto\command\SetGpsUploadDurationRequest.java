package com.yunqu.park.iot.model.dto.command;

import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.Max;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 设置GPS上传时长请求
 * 
 * <AUTHOR>
 * @since 2025-01-06
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class SetGpsUploadDurationRequest extends BaseCommandReq {
    
    /**
     * GPS上传时长 (秒: 1-3600)
     */
    @NotNull(message = "GPS上传时长不能为空")
    @Min(value = 1, message = "GPS上传时长不能小于1秒")
    @Max(value = 3600, message = "GPS上传时长不能大于3600秒")
    private Integer duration;
}