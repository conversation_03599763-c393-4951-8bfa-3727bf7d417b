package com.yunqu.park.iot.service.command.impl;

import com.yunqu.park.iot.model.command.CommandType;
import com.yunqu.park.iot.model.command.dto.SetAngleUploadParams;
import com.yunqu.park.iot.service.command.ICommand;
import lombok.RequiredArgsConstructor;

@RequiredArgsConstructor
public class SetAngleUploadCommand implements ICommand {

    private final SetAngleUploadParams params;
    private static final String TEMPLATE = "ANGLEREP,%s#";

    @Override
    public CommandType getType() {
        return CommandType.SET_ANGLE_UPLOAD;
    }

    @Override
    public String build() {
        return String.format(TEMPLATE, params.getState());
    }

    @Override
    public String getDescription() {
        return String.format("%s: %s", getType().getDescription(), params.getState());
    }
}