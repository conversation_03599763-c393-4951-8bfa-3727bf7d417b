package com.yunqu.park.iot.service.command;

import com.yunqu.park.iot.model.command.CommandType;

/**
 * IoT设备指令策略接口
 *
 * <p>定义了所有IoT设备指令的统一行为规范，是策略模式的核心接口。
 * 每个具体的指令类型都必须实现此接口，提供指令构建、类型识别和描述功能。</p>
 *
 * <h3>策略模式设计：</h3>
 * <ul>
 *   <li><strong>策略接口</strong>：ICommand - 定义统一的指令行为</li>
 *   <li><strong>具体策略</strong>：RebootCommand, SetIpCommand等 - 实现具体指令逻辑</li>
 *   <li><strong>策略工厂</strong>：CommandFactory - 根据类型创建策略实例</li>
 *   <li><strong>策略上下文</strong>：IotCommandServiceImpl - 使用策略执行指令</li>
 * </ul>
 *
 * <h3>实现要求：</h3>
 * <ul>
 *   <li><strong>类型唯一性</strong>：每个实现类必须返回唯一的CommandType</li>
 *   <li><strong>协议兼容性</strong>：build()方法必须生成符合设备协议的指令字符串</li>
 *   <li><strong>描述完整性</strong>：getDescription()应包含关键参数信息用于日志记录</li>
 *   <li><strong>无状态设计</strong>：实现类应该是无状态的，线程安全的</li>
 * </ul>
 *
 * <h3>指令协议格式：</h3>
 * <p>大多数指令遵循以下格式：</p>
 * <pre>{@code
 * // 无参数指令
 * "RESET#"                    // 重启指令
 * "STATUS#"                   // 状态查询
 *
 * // 有参数指令
 * "SERVER,1,*************,8080#"  // 设置IP
 * "SPEED,1,80#"                    // 设置超速报警
 * }</pre>
 *
 * <h3>实现示例：</h3>
 * <pre>{@code
 * @RequiredArgsConstructor
 * public class SetIpCommand implements ICommand {
 *     private final SetIpParams params;
 *     private static final String TEMPLATE = "SERVER,1,%s,%d#";
 *
 *     @Override
 *     public CommandType getType() {
 *         return CommandType.SET_IP;
 *     }
 *
 *     @Override
 *     public String build() {
 *         return String.format(TEMPLATE, params.getIp(), params.getPort());
 *     }
 *
 *     @Override
 *     public String getDescription() {
 *         return String.format("设置IP: %s:%d", params.getIp(), params.getPort());
 *     }
 * }
 * }</pre>
 *
 * <h3>扩展指南：</h3>
 * <ol>
 *   <li>创建新的指令实现类，实现ICommand接口</li>
 *   <li>定义指令模板字符串（符合设备协议）</li>
 *   <li>实现三个必需方法：getType(), build(), getDescription()</li>
 *   <li>在CommandFactory中注册新的指令类型</li>
 *   <li>编写单元测试验证指令格式正确性</li>
 * </ol>
 *
 * <AUTHOR>
 * @version 2.0
 * @since 2025-01-06
 * @see com.yunqu.park.iot.service.command.CommandFactory
 * @see com.yunqu.park.iot.model.command.CommandType
 * @see com.yunqu.park.iot.service.command.impl
 */
public interface ICommand {

    /**
     * 获取该指令的唯一类型标识.
     *
     * @return CommandType 枚举实例
     */
    CommandType getType();

    /**
     * 根据指令参数构建最终要发送给设备的原始指令字符串.
     *
     * @return 符合协议规范的指令字符串
     */
    String build();

    /**
     * 获取用于日志记录和历史追溯的指令业务描述.
     *
     * @return 指令的中文描述, 通常包含关键参数
     */
    String getDescription();
}