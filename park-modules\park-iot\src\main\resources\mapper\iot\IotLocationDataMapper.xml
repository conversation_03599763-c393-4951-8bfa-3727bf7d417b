<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yunqu.park.iot.mapper.IotLocationDataMapper">

    <resultMap type="com.yunqu.park.iot.domain.IotLocationData" id="IotLocationDataResult">
        <result property="locationId"    column="location_id"    />
        <result property="deviceId"    column="device_id"    />
        <result property="imei"    column="imei"    />
        <result property="gpsTime"    column="gps_time"    />
        <result property="latitude"    column="latitude"    />
        <result property="longitude"    column="longitude"    />
        <result property="altitude"    column="altitude"    />
        <result property="speed"    column="speed"    />
        <result property="direction"    column="direction"    />
        <result property="satelliteCount"    column="satellite_count"    />
        <result property="gpsStatus"    column="gps_status"    />
        <result property="accStatus"    column="acc_status"    />
        <result property="mcc"    column="mcc"    />
        <result property="mnc"    column="mnc"    />
        <result property="lac"    column="lac"    />
        <result property="cellId"    column="cell_id"    />
        <result property="signalStrength"    column="signal_strength"    />
        <result property="mileage"    column="mileage"    />
        <result property="tenantId"    column="tenant_id"    />
        <result property="createTime"    column="create_time"    />
    </resultMap>

    <resultMap type="com.yunqu.park.iot.domain.vo.IotLocationDataVo" id="IotLocationDataVoResult">
        <result property="locationId"    column="location_id"    />
        <result property="deviceId"    column="device_id"    />
        <result property="imei"    column="imei"    />
        <result property="gpsTime"    column="gps_time"    />
        <result property="latitude"    column="latitude"    />
        <result property="longitude"    column="longitude"    />
        <result property="altitude"    column="altitude"    />
        <result property="speed"    column="speed"    />
        <result property="direction"    column="direction"    />
        <result property="satelliteCount"    column="satellite_count"    />
        <result property="gpsStatus"    column="gps_status"    />
        <result property="accStatus"    column="acc_status"    />
        <result property="mcc"    column="mcc"    />
        <result property="mnc"    column="mnc"    />
        <result property="lac"    column="lac"    />
        <result property="cellId"    column="cell_id"    />
        <result property="signalStrength"    column="signal_strength"    />
        <result property="mileage"    column="mileage"    />
        <result property="tenantId"    column="tenant_id"    />
        <result property="createTime"    column="create_time"    />
    </resultMap>

    <sql id="selectIotLocationDataVo">
        select location_id, device_id, imei, gps_time, latitude, longitude, altitude, speed, direction, satellite_count, gps_status, acc_status, mcc, mnc, lac, cell_id, signal_strength, mileage, tenant_id, create_time from iot_location_data
    </sql>

    <select id="selectLatestByImei" parameterType="String" resultMap="IotLocationDataVoResult">
        <include refid="selectIotLocationDataVo"/>
        where imei = #{imei}
        order by gps_time desc
        limit 1
    </select>

    <select id="selectTrackData" resultMap="IotLocationDataVoResult">
        <include refid="selectIotLocationDataVo"/>
        where imei = #{imei}
        and gps_time between #{startTime} and #{endTime}
        order by gps_time asc
    </select>

    <select id="selectPlaybackData" resultMap="IotLocationDataVoResult">
        <include refid="selectIotLocationDataVo"/>
        where imei = #{imei}
        and gps_time between #{startTime} and #{endTime}
        and UNIX_TIMESTAMP(gps_time) % #{interval} = 0
        order by gps_time asc
    </select>

    <insert id="batchInsert" parameterType="java.util.List">
        insert into iot_location_data
        (location_id, device_id, imei, gps_time, latitude, longitude, altitude, speed, direction, 
         satellite_count, gps_status, acc_status, mcc, mnc, lac, cell_id, signal_strength, mileage, 
         tenant_id, create_time)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.locationId}, #{item.deviceId}, #{item.imei}, #{item.gpsTime}, #{item.latitude}, 
             #{item.longitude}, #{item.altitude}, #{item.speed}, #{item.direction}, #{item.satelliteCount}, 
             #{item.gpsStatus}, #{item.accStatus}, #{item.mcc}, #{item.mnc}, #{item.lac}, #{item.cellId}, 
             #{item.signalStrength}, #{item.mileage}, #{item.tenantId}, #{item.createTime})
        </foreach>
    </insert>

</mapper>
