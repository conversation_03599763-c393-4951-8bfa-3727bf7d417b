package com.yunqu.park.common.web.config;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.convert.converter.Converter;
import org.springframework.stereotype.Component;

import java.util.Date;

/**
 * 全局日期类型转换器配置
 * 解决前端传递空字符串无法转换为Date类型的问题
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class GlobalDateConverterConfig {

    /**
     * String转Date转换器
     * 处理空字符串和各种日期格式
     */
    @Component
    public static class StringToDateConverter implements Converter<String, Date> {

        @Override
        public Date convert(String source) {
            try {
                // 如果是空字符串或null，返回null
                if (StrUtil.isBlank(source)) {
                    log.debug("Converting blank string to null Date");
                    return null;
                }

                // 去除前后空格
                source = source.trim();

                // 如果是空字符串，返回null
                if (source.isEmpty()) {
                    log.debug("Converting empty string to null Date");
                    return null;
                }

                // 特殊处理常见的无效值
                if ("null".equalsIgnoreCase(source) || "undefined".equalsIgnoreCase(source)) {
                    log.debug("Converting '{}' to null Date", source);
                    return null;
                }

                // 尝试解析时间戳（毫秒）
                if (source.matches("\\d{13}")) {
                    long timestamp = Long.parseLong(source);
                    log.debug("Converting timestamp {} to Date", timestamp);
                    return new Date(timestamp);
                }

                // 尝试解析时间戳（秒）
                if (source.matches("\\d{10}")) {
                    long timestamp = Long.parseLong(source) * 1000;
                    log.debug("Converting timestamp {} (seconds) to Date", source);
                    return new Date(timestamp);
                }

                // 使用hutool的DateUtil进行智能解析
                // 支持多种日期格式：yyyy-MM-dd、yyyy-MM-dd HH:mm:ss、yyyy/MM/dd等
                Date result = DateUtil.parse(source);
                log.debug("Successfully converted '{}' to Date: {}", source, result);
                return result;

            } catch (Exception e) {
                log.warn("Failed to convert string '{}' to Date: {}, returning null", source, e.getMessage());
                return null;
            }
        }
    }

    /**
     * String转java.sql.Date转换器
     */
    @Component
    public static class StringToSqlDateConverter implements Converter<String, java.sql.Date> {

        @Override
        public java.sql.Date convert(String source) {
            try {
                // 如果是空字符串或null，返回null
                if (StrUtil.isBlank(source)) {
                    return null;
                }

                // 去除前后空格
                source = source.trim();

                // 如果是空字符串，返回null
                if (source.isEmpty()) {
                    return null;
                }

                // 特殊处理常见的无效值
                if ("null".equalsIgnoreCase(source) || "undefined".equalsIgnoreCase(source)) {
                    return null;
                }

                // 先转换为java.util.Date，再转换为java.sql.Date
                Date utilDate = new StringToDateConverter().convert(source);
                return utilDate != null ? new java.sql.Date(utilDate.getTime()) : null;

            } catch (Exception e) {
                log.warn("Failed to convert string '{}' to java.sql.Date: {}, returning null", source, e.getMessage());
                return null;
            }
        }
    }

    /**
     * String转java.sql.Timestamp转换器
     */
    @Component
    public static class StringToTimestampConverter implements Converter<String, java.sql.Timestamp> {

        @Override
        public java.sql.Timestamp convert(String source) {
            try {
                // 如果是空字符串或null，返回null
                if (StrUtil.isBlank(source)) {
                    return null;
                }

                // 去除前后空格
                source = source.trim();

                // 如果是空字符串，返回null
                if (source.isEmpty()) {
                    return null;
                }

                // 特殊处理常见的无效值
                if ("null".equalsIgnoreCase(source) || "undefined".equalsIgnoreCase(source)) {
                    return null;
                }

                // 先转换为java.util.Date，再转换为java.sql.Timestamp
                Date utilDate = new StringToDateConverter().convert(source);
                return utilDate != null ? new java.sql.Timestamp(utilDate.getTime()) : null;

            } catch (Exception e) {
                log.warn("Failed to convert string '{}' to java.sql.Timestamp: {}, returning null", source, e.getMessage());
                return null;
            }
        }
    }
}
