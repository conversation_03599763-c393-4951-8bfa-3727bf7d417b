package com.yunqu.park.iot.model.command.dto;

import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = false)
public class SetVibrationSensitivityParams extends CommandParams {
    @NotNull(message = "灵敏度级别不能为空")
    @Min(value = 1, message = "灵敏度级别必须在1-10之间")
    @Max(value = 10, message = "灵敏度级别必须在1-10之间")
    private Integer level;
}