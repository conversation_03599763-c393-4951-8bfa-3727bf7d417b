package com.yunqu.park.iot.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.yunqu.park.common.tenant.core.TenantEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 设备定位数据对象 iot_location_data
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("iot_location_data")
public class IotLocationData extends TenantEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 定位记录ID
     */
    @TableId(value = "location_id")
    private Long locationId;

    /**
     * 设备ID
     */
    private Long deviceId;

    /**
     * 设备IMEI
     */
    private String imei;

    /**
     * GPS时间
     */
    private Date gpsTime;

    /**
     * 纬度
     */
    private BigDecimal latitude;

    /**
     * 经度
     */
    private BigDecimal longitude;

    /**
     * 海拔高度(米)
     */
    private Integer altitude;

    /**
     * 速度(km/h)
     */
    private Integer speed;

    /**
     * 方向角(0-360度)
     */
    private Integer direction;

    /**
     * 卫星数量
     */
    private Integer satelliteCount;

    /**
     * GPS状态:0-未定位,1-已定位
     */
    private String gpsStatus;

    /**
     * ACC状态:0-关闭,1-开启
     */
    private String accStatus;

    /**
     * 移动国家代码
     */
    private Integer mcc;

    /**
     * 移动网络代码
     */
    private Integer mnc;

    /**
     * 位置区码
     */
    private Integer lac;

    /**
     * 基站ID
     */
    private Integer cellId;

    /**
     * 信号强度(0-100)
     */
    private Integer signalStrength;

    /**
     * 里程(米)
     */
    private Integer mileage;

}
