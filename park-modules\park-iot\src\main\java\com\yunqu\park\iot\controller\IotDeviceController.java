package com.yunqu.park.iot.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.yunqu.park.common.core.domain.R;
import com.yunqu.park.common.core.validate.AddGroup;
import com.yunqu.park.common.core.validate.EditGroup;
import com.yunqu.park.common.excel.utils.ExcelUtil;
import com.yunqu.park.common.idempotent.annotation.RepeatSubmit;
import com.yunqu.park.common.log.annotation.Log;
import com.yunqu.park.common.log.enums.BusinessType;
import com.yunqu.park.common.mybatis.core.page.PageQuery;
import com.yunqu.park.common.mybatis.core.page.TableDataInfo;
import com.yunqu.park.common.web.core.BaseController;
import com.yunqu.park.iot.domain.bo.IotDeviceBo;
import com.yunqu.park.iot.domain.vo.IotDeviceVo;
import com.yunqu.park.iot.service.IIotDeviceService;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * IoT设备管理
 *
 * <AUTHOR>
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/iot/device")
public class IotDeviceController extends BaseController {

    private final IIotDeviceService iotDeviceService;

    /**
     * 查询IoT设备列表
     */
    @SaCheckPermission("iot:device:list")
    @GetMapping("/list")
    public TableDataInfo<IotDeviceVo> list(IotDeviceBo bo, PageQuery pageQuery) {
        return iotDeviceService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出IoT设备列表
     */
    @SaCheckPermission("iot:device:export")
    @Log(title = "IoT设备", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(IotDeviceBo bo, HttpServletResponse response) {
        List<IotDeviceVo> list = iotDeviceService.queryList(bo);
        ExcelUtil.exportExcel(list, "IoT设备", IotDeviceVo.class, response);
    }

    /**
     * 获取IoT设备详细信息
     */
    @SaCheckPermission("iot:device:query")
    @GetMapping("/{deviceId}")
    public R<IotDeviceVo> getInfo(@NotNull(message = "主键不能为空")
                                  @PathVariable Long deviceId) {
        return R.ok(iotDeviceService.queryById(deviceId));
    }

    /**
     * 新增IoT设备
     */
    @SaCheckPermission("iot:device:add")
    @Log(title = "IoT设备", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody IotDeviceBo bo) {
        return toAjax(iotDeviceService.insertByBo(bo));
    }

    /**
     * 修改IoT设备
     */
    @SaCheckPermission("iot:device:edit")
    @Log(title = "IoT设备", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody IotDeviceBo bo) {
        return toAjax(iotDeviceService.updateByBo(bo));
    }

    /**
     * 删除IoT设备
     */
    @SaCheckPermission("iot:device:remove")
    @Log(title = "IoT设备", businessType = BusinessType.DELETE)
    @DeleteMapping("/{deviceIds}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] deviceIds) {
        return toAjax(iotDeviceService.deleteWithValidByIds(List.of(deviceIds), true));
    }

    /**
     * 根据IMEI查询设备信息
     */
    @SaCheckPermission("iot:device:query")
    @GetMapping("/imei/{imei}")
    public R<IotDeviceVo> getByImei(@PathVariable String imei) {
        return R.ok(iotDeviceService.queryByImei(imei));
    }

    /**
     * 获取设备统计信息
     */
    @SaCheckPermission("iot:device:list")
    @GetMapping("/statistics")
    public R<Object> getStatistics() {
        return R.ok(iotDeviceService.getDeviceStatistics());
    }
}
