package com.yunqu.park.iot.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.yunqu.park.common.excel.annotation.ExcelDictFormat;
import com.yunqu.park.common.excel.convert.ExcelDictConvert;
import com.yunqu.park.iot.domain.IotAlarmRecord;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 设备报警记录视图对象 iot_alarm_record
 *
 * <AUTHOR>
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = IotAlarmRecord.class)
public class IotAlarmRecordVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 报警记录ID
     */
    @ExcelProperty(value = "报警记录ID")
    private Long alarmId;

    /**
     * 设备ID
     */
    @ExcelProperty(value = "设备ID")
    private Long deviceId;

    /**
     * 设备IMEI
     */
    @ExcelProperty(value = "设备IMEI")
    private String imei;

    /**
     * 报警类型
     */
    @ExcelProperty(value = "报警类型", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "iot_alarm_type")
    private Integer alarmType;

    /**
     * 报警名称
     */
    @ExcelProperty(value = "报警名称")
    private String alarmName;

    /**
     * 报警时间
     */
    @ExcelProperty(value = "报警时间")
    private Date alarmTime;

    /**
     * 报警位置纬度
     */
    @ExcelProperty(value = "报警位置纬度")
    private BigDecimal latitude;

    /**
     * 报警位置经度
     */
    @ExcelProperty(value = "报警位置经度")
    private BigDecimal longitude;

    /**
     * 报警地址
     */
    @ExcelProperty(value = "报警地址")
    private String address;

    /**
     * 处理状态:0-未处理,1-已处理
     */
    @ExcelProperty(value = "处理状态", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "iot_alarm_status")
    private String alarmStatus;

    /**
     * 处理时间
     */
    @ExcelProperty(value = "处理时间")
    private Date handleTime;

    /**
     * 处理人
     */
    @ExcelProperty(value = "处理人")
    private Long handleUser;

    /**
     * 处理备注
     */
    @ExcelProperty(value = "处理备注")
    private String handleRemark;

    /**
     * 租户编号
     */
    private String tenantId;

    /**
     * 创建部门
     */
    private Long createDept;

    /**
     * 创建者
     */
    private Long createBy;

    /**
     * 创建时间
     */
    @ExcelProperty(value = "创建时间")
    private Date createTime;

    /**
     * 更新者
     */
    private Long updateBy;

    /**
     * 更新时间
     */
    private Date updateTime;

}
