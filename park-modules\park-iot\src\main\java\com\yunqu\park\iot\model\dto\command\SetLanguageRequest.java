package com.yunqu.park.iot.model.dto.command;

import com.yunqu.park.iot.model.command.dto.SetLanguageParams;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 设置语言指令请求
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "设置语言指令请求")
public class SetLanguageRequest extends BaseCommandReq {

    @Schema(description = "语言参数")
    private SetLanguageParams params;
}