package com.yunqu.park.iot.model.dto.command;

import com.yunqu.park.iot.model.command.dto.SetApnParams;
import lombok.Data;
import lombok.EqualsAndHashCode;

import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;

/**
 * 设置APN指令请求
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class SetApnRequest extends BaseCommandReq {

    /**
     * APN设置参数
     */
    @Valid
    @NotNull(message = "APN参数不能为空")
    private SetApnParams params;
}