package com.yunqu.park.iot.netty.protocol;

import lombok.Data;
import lombok.Builder;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

/**
 * LBS基站数据模型
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class LbsData {

    /**
     * 移动国家码 (Mobile Country Code)
     */
    private int mcc;

    /**
     * 移动网络码 (Mobile Network Code)
     */
    private int mnc;

    /**
     * 位置区域码 (Location Area Code)
     */
    private int lac;

    /**
     * 小区ID (Cell ID)
     */
    private int cellId;
}