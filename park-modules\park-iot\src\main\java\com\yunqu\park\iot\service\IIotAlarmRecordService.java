package com.yunqu.park.iot.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.yunqu.park.common.mybatis.core.page.PageQuery;
import com.yunqu.park.common.mybatis.core.page.TableDataInfo;
import com.yunqu.park.iot.domain.IotAlarmRecord;
import com.yunqu.park.iot.domain.bo.IotAlarmRecordBo;
import com.yunqu.park.iot.domain.vo.IotAlarmRecordVo;

import java.util.Collection;
import java.util.Date;
import java.util.List;

/**
 * 设备报警记录Service接口
 *
 * <AUTHOR>
 */
public interface IIotAlarmRecordService extends IService<IotAlarmRecord> {



    /**
     * 查询设备报警记录
     */
    IotAlarmRecordVo queryById(Long alarmId);

    /**
     * 查询设备报警记录列表
     */
    TableDataInfo<IotAlarmRecordVo> queryPageList(IotAlarmRecordBo bo, PageQuery pageQuery);

    /**
     * 查询设备报警记录列表
     */
    List<IotAlarmRecordVo> queryList(IotAlarmRecordBo bo);

    /**
     * 根据新增业务对象插入设备报警记录
     */
    Boolean insertByBo(IotAlarmRecordBo bo);

    /**
     * 根据编辑业务对象修改设备报警记录
     */
    Boolean updateByBo(IotAlarmRecordBo bo);

    /**
     * 校验并批量删除设备报警记录信息
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    /**
     * 处理报警记录
     * @param alarmId 报警记录ID
     * @param handleUser 处理人
     * @param handleRemark 处理备注
     * @return 是否成功
     */
    Boolean handleAlarm(Long alarmId, Long handleUser, String handleRemark);

    /**
     * 根据设备IMEI查询未处理报警
     * @param imei 设备IMEI号
     * @return 未处理报警列表
     */
    List<IotAlarmRecordVo> getUnhandledAlarms(String imei);

    /**
     * 根据报警类型统计报警数量
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 统计结果
     */
    Object getAlarmStatistics(Date startTime, Date endTime);

    /**
     * 保存报警记录
     * @param alarmRecord 报警记录
     * @return 是否成功
     */
    Boolean saveAlarmRecord(IotAlarmRecord alarmRecord);

    /**
     * 根据报警类型获取报警名称
     * @param alarmType 报警类型
     * @return 报警名称
     */
    String getAlarmName(Integer alarmType);
}
