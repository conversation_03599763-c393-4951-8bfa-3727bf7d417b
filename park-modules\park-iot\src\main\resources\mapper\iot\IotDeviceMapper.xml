<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yunqu.park.iot.mapper.IotDeviceMapper">

    <resultMap type="com.yunqu.park.iot.domain.IotDevice" id="IotDeviceResult">
        <result property="deviceId"    column="device_id"    />
        <result property="imei"    column="imei"    />
        <result property="deviceName"    column="device_name"    />
        <result property="deviceType"    column="device_type"    />
        <result property="simImsi"    column="sim_imsi"    />
        <result property="simIccid"    column="sim_iccid"    />
        <result property="status"    column="status"    />
        <result property="lastOnlineTime"    column="last_online_time"    />
        <result property="registerTime"    column="register_time"    />
        <result property="remark"    column="remark"    />
        <result property="tenantId"    column="tenant_id"    />
        <result property="createDept"    column="create_dept"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="delFlag"    column="del_flag"    />
    </resultMap>

    <resultMap type="com.yunqu.park.iot.domain.vo.IotDeviceVo" id="IotDeviceVoResult">
        <result property="deviceId"    column="device_id"    />
        <result property="imei"    column="imei"    />
        <result property="deviceName"    column="device_name"    />
        <result property="deviceType"    column="device_type"    />
        <result property="simImsi"    column="sim_imsi"    />
        <result property="simIccid"    column="sim_iccid"    />
        <result property="status"    column="status"    />
        <result property="lastOnlineTime"    column="last_online_time"    />
        <result property="registerTime"    column="register_time"    />
        <result property="remark"    column="remark"    />
        <result property="tenantId"    column="tenant_id"    />
        <result property="createTime"    column="create_time"    />
    </resultMap>

    <sql id="selectIotDeviceVo">
        select device_id, imei, device_name, device_type, sim_imsi, sim_iccid, status, last_online_time, register_time, remark, tenant_id, create_dept, create_by, create_time, update_by, update_time, del_flag from iot_device
    </sql>

    <select id="selectByImei" parameterType="String" resultMap="IotDeviceVoResult">
        <include refid="selectIotDeviceVo"/>
        where imei = #{imei} and del_flag = '0'
    </select>

    <select id="selectDeviceByImei" parameterType="String" resultMap="IotDeviceResult">
        <include refid="selectIotDeviceVo"/>
        where imei = #{imei} and del_flag = '0'
    </select>

    <update id="updateDeviceStatus">
        update iot_device 
        set status = #{status}, 
            last_online_time = now(),
            update_time = now()
        where imei = #{imei}
    </update>

    <update id="updateDeviceSimInfo">
        update iot_device 
        set 
        <if test="imsi != null and imsi != ''">
            sim_imsi = #{imsi},
        </if>
        <if test="iccid != null and iccid != ''">
            sim_iccid = #{iccid},
        </if>
        update_time = now()
        where imei = #{imei}
    </update>

    <select id="selectDeviceStatistics" resultType="java.util.Map">
        select 
            count(*) as total_count,
            sum(case when status = '1' then 1 else 0 end) as online_count,
            sum(case when status = '0' then 1 else 0 end) as offline_count,
            sum(case when status = '2' then 1 else 0 end) as sleep_count,
            count(distinct device_type) as device_type_count,
            max(last_online_time) as latest_online_time
        from iot_device 
        where del_flag = '0'
    </select>

</mapper>
