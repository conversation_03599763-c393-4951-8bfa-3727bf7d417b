package com.yunqu.park.iot.netty.handler;

/**
 * 连接断开原因枚举
 *
 * <AUTHOR>
 */
public enum DisconnectReason {
    /**
     * 客户端主动关闭连接
     */
    CLIENT_CLOSED("客户端主动关闭"),
    
    /**
     * 读空闲超时
     */
    READER_IDLE_TIMEOUT("读空闲超时"),
    
    /**
     * 全部空闲超时
     */
    ALL_IDLE_TIMEOUT("全部空闲超时"),
    
    /**
     * 网络错误
     */
    NETWORK_ERROR("网络错误"),
    
    /**
     * 协议错误
     */
    PROTOCOL_ERROR("协议错误"),
    
    /**
     * 内存错误
     */
    MEMORY_ERROR("内存错误"),
    
    /**
     * 异常断开
     */
    EXCEPTION("异常断开"),
    
    /**
     * 服务器关闭
     */
    SERVER_SHUTDOWN("服务器关闭");
    
    private final String description;
    
    DisconnectReason(String description) {
        this.description = description;
    }
    
    public String getDescription() {
        return description;
    }
}