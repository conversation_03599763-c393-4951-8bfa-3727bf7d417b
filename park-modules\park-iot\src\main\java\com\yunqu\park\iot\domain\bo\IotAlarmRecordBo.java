package com.yunqu.park.iot.domain.bo;

import com.yunqu.park.common.core.validate.AddGroup;
import com.yunqu.park.common.core.validate.EditGroup;
import com.yunqu.park.common.mybatis.core.domain.BaseEntity;
import com.yunqu.park.iot.domain.IotAlarmRecord;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.EqualsAndHashCode;

import jakarta.validation.constraints.*;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 设备报警记录业务对象 iot_alarm_record
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = IotAlarmRecord.class, reverseConvertGenerate = false)
public class IotAlarmRecordBo extends BaseEntity {

    /**
     * 报警记录ID
     */
    @NotNull(message = "报警记录ID不能为空", groups = { EditGroup.class })
    private Long alarmId;

    /**
     * 设备ID
     */
    @NotNull(message = "设备ID不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long deviceId;

    /**
     * 设备IMEI
     */
    @NotBlank(message = "设备IMEI不能为空", groups = { AddGroup.class, EditGroup.class })
    private String imei;

    /**
     * 报警类型
     */
    @NotNull(message = "报警类型不能为空", groups = { AddGroup.class, EditGroup.class })
    private Integer alarmType;

    /**
     * 报警名称
     */
    private String alarmName;

    /**
     * 报警时间
     */
    @NotNull(message = "报警时间不能为空", groups = { AddGroup.class, EditGroup.class })
    private Date alarmTime;

    /**
     * 报警位置纬度
     */
    @DecimalMin(value = "-90.0", message = "纬度范围错误")
    @DecimalMax(value = "90.0", message = "纬度范围错误")
    private BigDecimal latitude;

    /**
     * 报警位置经度
     */
    @DecimalMin(value = "-180.0", message = "经度范围错误")
    @DecimalMax(value = "180.0", message = "经度范围错误")
    private BigDecimal longitude;

    /**
     * 报警地址
     */
    @Size(max = 200, message = "报警地址不能超过200个字符")
    private String address;

    /**
     * 处理状态:0-未处理,1-已处理
     */
    private String alarmStatus;

    /**
     * 处理时间
     */
    private Date handleTime;

    /**
     * 处理人
     */
    private Long handleUser;

    /**
     * 处理备注
     */
    @Size(max = 500, message = "处理备注不能超过500个字符")
    private String handleRemark;

}
