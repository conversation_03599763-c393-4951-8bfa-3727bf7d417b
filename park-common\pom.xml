<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>park-manager-system</artifactId>
        <groupId>com.yunqu.park</groupId>
        <version>${revision}</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <modules>
        <module>park-common-bom</module>
        <module>park-common-social</module>
        <module>park-common-core</module>
        <module>park-common-doc</module>
        <module>park-common-excel</module>
        <module>park-common-idempotent</module>
        <module>park-common-job</module>
        <module>park-common-log</module>
        <module>park-common-mail</module>
        <module>park-common-mybatis</module>
        <module>park-common-oss</module>
        <module>park-common-ratelimiter</module>
        <module>park-common-redis</module>
        <module>park-common-satoken</module>
        <module>park-common-security</module>
        <module>park-common-sms</module>
        <module>park-common-web</module>
        <module>park-common-translation</module>
        <module>park-common-sensitive</module>
        <module>park-common-json</module>
        <module>park-common-encrypt</module>
        <module>park-common-tenant</module>
        <module>park-common-websocket</module>
        <module>park-common-sse</module>
    </modules>

    <artifactId>park-common</artifactId>
    <packaging>pom</packaging>

    <description>
        common 通用模块
    </description>

</project>
