package com.yunqu.park.iot.service.command.impl;

import com.yunqu.park.iot.model.command.CommandType;
import com.yunqu.park.iot.model.command.dto.SetStaticSleepParams;
import com.yunqu.park.iot.service.command.ICommand;
import lombok.RequiredArgsConstructor;

@RequiredArgsConstructor
public class SetStaticSleepCommand implements ICommand {

    private final SetStaticSleepParams params;
    private static final String TEMPLATE = "%s#";

    @Override
    public CommandType getType() {
        return CommandType.SET_STATIC_SLEEP;
    }

    @Override
    public String build() {
        return String.format(TEMPLATE, params.getCommand());
    }

    @Override
    public String getDescription() {
        return String.format("%s: %s", getType().getDescription(), params.getCommand());
    }
}