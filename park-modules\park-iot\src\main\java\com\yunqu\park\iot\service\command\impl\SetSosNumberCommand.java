package com.yunqu.park.iot.service.command.impl;

import com.yunqu.park.iot.model.command.CommandType;
import com.yunqu.park.iot.model.command.dto.SetSosNumberParams;
import com.yunqu.park.iot.service.command.ICommand;
import lombok.RequiredArgsConstructor;
import org.springframework.util.StringUtils;

@RequiredArgsConstructor
public class SetSosNumberCommand implements ICommand {

    private final SetSosNumberParams params;
    private static final String TEMPLATE = "SOS,A,%s,%s,%s#";

    @Override
    public CommandType getType() {
        return CommandType.SET_SOS_NUMBER;
    }

    @Override
    public String build() {
        String p1 = StringUtils.hasText(params.getPhone1()) ? params.getPhone1() : "";
        String p2 = StringUtils.hasText(params.getPhone2()) ? params.getPhone2() : "";
        String p3 = StringUtils.hasText(params.getPhone3()) ? params.getPhone3() : "";
        return String.format(TEMPLATE, p1, p2, p3);
    }

    @Override
    public String getDescription() {
        return String.format("%s: %s, %s, %s", getType().getDescription(), params.getPhone1(), params.getPhone2(), params.getPhone3());
    }
}