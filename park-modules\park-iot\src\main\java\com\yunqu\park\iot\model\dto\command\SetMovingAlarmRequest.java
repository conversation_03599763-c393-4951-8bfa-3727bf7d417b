package com.yunqu.park.iot.model.dto.command;

import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 设置移动报警请求
 * 
 * <AUTHOR>
 * @since 2025-01-06
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class SetMovingAlarmRequest extends BaseCommandReq {
    
    /**
     * 是否启用移动报警
     * true: 启用, false: 禁用
     */
    @NotNull(message = "移动报警开关不能为空")
    private Boolean enabled;
    
    /**
     * 移动报警灵敏度 (1-10, 1最敏感)
     */
    private Integer sensitivity;
}