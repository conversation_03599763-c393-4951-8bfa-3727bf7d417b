package com.yunqu.park.iot.model.dto.command;

import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 设置断电报警请求
 * 
 * <AUTHOR>
 * @since 2025-01-06
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class SetPowerAlarmRequest extends BaseCommandReq {
    
    /**
     * 是否启用断电报警
     * true: 启用, false: 禁用
     */
    @NotNull(message = "断电报警开关不能为空")
    private Boolean enabled;
}