package com.yunqu.park.iot.utils;

import io.netty.buffer.ByteBuf;
import io.netty.buffer.ByteBufAllocator;
import io.netty.buffer.PooledByteBufAllocator;
import io.netty.buffer.Unpooled;
import lombok.extern.slf4j.Slf4j;

/**
 * ByteBuf优化工具类
 * 阶段2优化：减少内存分配和拷贝操作
 *
 * <AUTHOR>
 */
@Slf4j
public class ByteBufOptimizer {

    /**
     * 池化的ByteBuf分配器 - 阶段2优化：使用池化分配器减少GC压力
     */
    private static final ByteBufAllocator POOLED_ALLOCATOR = PooledByteBufAllocator.DEFAULT;

    /**
     * 优化的字节读取 - 零拷贝版本
     * 阶段2优化：使用slice()而不是copy()，避免数据拷贝
     * 
     * @param buffer 源缓冲区
     * @param length 读取长度
     * @return 读取的字节数组，如果数据不足返回null
     */
    public static byte[] readBytesOptimized(ByteBuf buffer, int length) {
        if (buffer == null || length <= 0) {
            return null;
        }

        if (buffer.readableBytes() < length) {
            log.debug("[BYTEBUF-OPT] 数据不足: 需要{}字节, 可用{}字节", length, buffer.readableBytes());
            return null;
        }

        try {
            // 使用slice创建视图，避免数据拷贝
            ByteBuf slice = buffer.readSlice(length);
            
            // 只有在必要时才进行数据拷贝
            byte[] result = new byte[length];
            slice.getBytes(0, result);
            
            return result;
            
        } catch (Exception e) {
            log.warn("[BYTEBUF-OPT] 读取字节时发生异常: {}", e.getMessage());
            return null;
        }
    }

    /**
     * 优化的单字节读取
     * 阶段2优化：直接读取，避免创建数组
     * 
     * @param buffer 源缓冲区
     * @return 读取的字节，如果数据不足返回null
     */
    public static Byte readByteOptimized(ByteBuf buffer) {
        if (buffer == null || buffer.readableBytes() < 1) {
            return null;
        }

        try {
            return buffer.readByte();
        } catch (Exception e) {
            log.warn("[BYTEBUF-OPT] 读取字节时发生异常: {}", e.getMessage());
            return null;
        }
    }

    /**
     * 优化的ByteBuf转hex字符串
     * 阶段2优化：使用StringBuilder减少字符串拼接开销
     * 
     * @param buffer 源缓冲区
     * @param maxLength 最大读取长度
     * @return hex字符串
     */
    public static String bufToHexStringOptimized(ByteBuf buffer, int maxLength) {
        if (buffer == null || buffer.readableBytes() == 0) {
            return "";
        }

        int length = Math.min(buffer.readableBytes(), maxLength);
        StringBuilder sb = new StringBuilder(length * 2);
        
        int readerIndex = buffer.readerIndex();
        try {
            for (int i = 0; i < length; i++) {
                byte b = buffer.getByte(readerIndex + i);
                sb.append(String.format("%02X", b & 0xFF));
            }
            return sb.toString();
        } catch (Exception e) {
            log.warn("[BYTEBUF-OPT] 转换hex字符串时发生异常: {}", e.getMessage());
            return "";
        }
    }

    /**
     * 优化的字节数组转hex字符串
     * 阶段2优化：使用查表法提高转换性能
     * 
     * @param bytes 字节数组
     * @return hex字符串
     */
    public static String bytesToHexStringOptimized(byte[] bytes) {
        if (bytes == null || bytes.length == 0) {
            return "";
        }

        // 使用查表法提高性能
        char[] hexChars = new char[bytes.length * 2];
        for (int i = 0; i < bytes.length; i++) {
            int v = bytes[i] & 0xFF;
            hexChars[i * 2] = HEX_ARRAY[v >>> 4];
            hexChars[i * 2 + 1] = HEX_ARRAY[v & 0x0F];
        }
        return new String(hexChars);
    }

    /**
     * hex字符查表 - 阶段2优化：预计算hex字符提高转换性能
     */
    private static final char[] HEX_ARRAY = "0123456789ABCDEF".toCharArray();

    /**
     * 创建池化的ByteBuf
     * 阶段2优化：使用池化分配器减少内存分配开销
     * 
     * @param capacity 容量
     * @return 池化的ByteBuf
     */
    public static ByteBuf createPooledBuffer(int capacity) {
        try {
            return POOLED_ALLOCATOR.buffer(capacity);
        } catch (Exception e) {
            log.warn("[BYTEBUF-OPT] 创建池化缓冲区失败，使用非池化缓冲区: {}", e.getMessage());
            return Unpooled.buffer(capacity);
        }
    }

    /**
     * 安全释放ByteBuf
     * 阶段2优化：确保ByteBuf正确释放，避免内存泄漏
     * 
     * @param buffer 要释放的缓冲区
     */
    public static void safeRelease(ByteBuf buffer) {
        if (buffer != null && buffer.refCnt() > 0) {
            try {
                buffer.release();
                log.debug("[BYTEBUF-OPT] ByteBuf已释放");
            } catch (Exception e) {
                log.warn("[BYTEBUF-OPT] 释放ByteBuf时发生异常: {}", e.getMessage());
            }
        }
    }

    /**
     * 检查ByteBuf是否可读
     * 阶段2优化：统一的可读性检查
     * 
     * @param buffer 缓冲区
     * @param requiredBytes 需要的字节数
     * @return 是否可读
     */
    public static boolean isReadable(ByteBuf buffer, int requiredBytes) {
        return buffer != null && buffer.readableBytes() >= requiredBytes;
    }

    /**
     * 获取池化分配器统计信息
     * 阶段2优化：监控内存池使用情况
     * 
     * @return 统计信息字符串
     */
    public static String getPoolStatistics() {
        try {
            if (POOLED_ALLOCATOR instanceof PooledByteBufAllocator) {
                PooledByteBufAllocator pooled = (PooledByteBufAllocator) POOLED_ALLOCATOR;
                return String.format("DirectMemory: %d bytes, HeapMemory: %d bytes",
                        pooled.metric().usedDirectMemory(),
                        pooled.metric().usedHeapMemory());
            }
            return "非池化分配器";
        } catch (Exception e) {
            return "统计信息获取失败: " + e.getMessage();
        }
    }

    /**
     * 复制ByteBuf内容（零拷贝版本）
     * 阶段2优化：使用slice避免数据拷贝
     * 
     * @param source 源缓冲区
     * @param offset 偏移量
     * @param length 长度
     * @return 复制的缓冲区
     */
    public static ByteBuf sliceCopy(ByteBuf source, int offset, int length) {
        if (source == null || offset < 0 || length <= 0) {
            return Unpooled.EMPTY_BUFFER;
        }

        if (offset + length > source.readableBytes()) {
            log.warn("[BYTEBUF-OPT] slice参数超出范围: offset={}, length={}, available={}",
                    offset, length, source.readableBytes());
            return Unpooled.EMPTY_BUFFER;
        }

        try {
            return source.slice(source.readerIndex() + offset, length).retain();
        } catch (Exception e) {
            log.warn("[BYTEBUF-OPT] slice操作失败: {}", e.getMessage());
            return Unpooled.EMPTY_BUFFER;
        }
    }

    /**
     * 比较两个ByteBuf是否相等
     * 阶段2优化：高效的ByteBuf比较
     * 
     * @param buf1 缓冲区1
     * @param buf2 缓冲区2
     * @return 是否相等
     */
    public static boolean equals(ByteBuf buf1, ByteBuf buf2) {
        if (buf1 == buf2) {
            return true;
        }
        
        if (buf1 == null || buf2 == null) {
            return false;
        }
        
        if (buf1.readableBytes() != buf2.readableBytes()) {
            return false;
        }
        
        return buf1.equals(buf2);
    }
}
