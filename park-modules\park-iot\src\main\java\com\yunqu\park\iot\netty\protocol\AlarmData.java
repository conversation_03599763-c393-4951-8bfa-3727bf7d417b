package com.yunqu.park.iot.netty.protocol;

import lombok.Data;
import lombok.Builder;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

/**
 * 报警数据模型
 * 用于封装从协议号 0x26 包中解析出的报警信息
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AlarmData {

    /**
     * 报警类型
     */
    private int type;

    /**
     * 报警类型描述
     */
    private String typeDescription;

    public static AlarmData from(byte alarmByte) {
        int type = alarmByte & 0xFF;
        String description = switch (type) {
            case 0x01 -> "SOS求救";
            case 0x02 -> "断电报警";
            case 0x03 -> "震动报警";
            case 0x04 -> "进围栏报警";
            case 0x05 -> "出围栏报警";
            case 0x06 -> "超速报警";
            case 0x09 -> "位移报警";
            case 0x0A -> "进GPS盲区报警";
            case 0x0B -> "出GPS盲区报警";
            case 0x0E -> "低电报警";
            default -> "未知报警 (" + String.format("0x%02X", type) + ")";
        };
        return AlarmData.builder()
            .type(type)
            .typeDescription(description)
            .build();
    }
}