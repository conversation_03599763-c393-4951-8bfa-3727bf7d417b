package com.yunqu.park.iot.model.dto.command;

import com.yunqu.park.iot.model.command.dto.DeleteSosNumberParams;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 删除SOS号码指令请求
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "删除SOS号码指令请求")
public class DeleteSosNumberRequest extends BaseCommandReq {

    @Schema(description = "删除SOS号码参数")
    private DeleteSosNumberParams params;
}