package com.yunqu.park.iot.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.yunqu.park.common.mybatis.core.page.PageQuery;
import com.yunqu.park.common.mybatis.core.page.TableDataInfo;
import com.yunqu.park.iot.domain.IotLocationData;
import com.yunqu.park.iot.domain.bo.IotLocationDataBo;
import com.yunqu.park.iot.domain.vo.IotLocationDataVo;

import java.util.Collection;
import java.util.Date;
import java.util.List;

/**
 * 设备定位数据Service接口
 *
 * <AUTHOR>
 */
public interface IIotLocationDataService extends IService<IotLocationData> {


    /**
     * 查询设备定位数据
     */
    IotLocationDataVo queryById(Long locationId);
    /**
     * 查询设备定位数据列表
     */
    TableDataInfo<IotLocationDataVo> queryPageList(IotLocationDataBo bo, PageQuery pageQuery);

    /**
     * 查询设备定位数据列表
     */
    List<IotLocationDataVo> queryList(IotLocationDataBo bo);

    /**
     * 根据新增业务对象插入设备定位数据
     */
    Boolean insertByBo(IotLocationDataBo bo);

    /**
     * 根据编辑业务对象修改设备定位数据
     */
    Boolean updateByBo(IotLocationDataBo bo);

    /**
     * 校验并批量删除设备定位数据信息
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    /**
     * 根据IMEI获取最新定位数据
     * @param imei 设备IMEI号
     * @return 最新定位数据
     */
    IotLocationDataVo getCurrentLocation(String imei);

    /**
     * 根据IMEI和时间范围查询轨迹数据
     * @param imei 设备IMEI号
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 轨迹数据列表
     */
    List<IotLocationDataVo> getTrackData(String imei, Date startTime, Date endTime);

    /**
     * 根据IMEI和时间范围查询回放数据(按间隔采样)
     * @param imei 设备IMEI号
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param interval 间隔秒数
     * @return 回放数据列表
     */
    List<IotLocationDataVo> getPlaybackData(String imei, Date startTime, Date endTime, Integer interval);

    /**
     * 批量保存定位数据
     * @param locationDataList 定位数据列表
     * @return 保存数量
     */
    int batchSaveLocationData(List<IotLocationData> locationDataList);

    /**
     * 保存单条定位数据
     * @param locationData 定位数据
     * @return 是否成功
     */
    Boolean saveLocationData(IotLocationData locationData);
}
