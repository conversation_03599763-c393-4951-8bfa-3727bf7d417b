package com.yunqu.park.iot.model.command.dto;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 通用报警设置参数.
 * 适用于: 震动报警, 断电报警, 低电报警, ACC报警.
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class SetAlarmParams extends CommandParams {
    @NotBlank(message = "状态不能为空 (ON/OFF/FULL)")
    private String state;

    @NotNull(message = "报警模式不能为空")
    private Integer mode;
}