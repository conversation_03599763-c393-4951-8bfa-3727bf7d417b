package com.yunqu.park.iot.model.dto.command;

import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.Max;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 设置测速模式请求
 * 
 * <AUTHOR>
 * @since 2025-01-06
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class SetSpeedMeasurementModeRequest extends BaseCommandReq {
    
    /**
     * 测速模式
     * 0: GPS测速, 1: 脉冲测速
     */
    @NotNull(message = "测速模式不能为空")
    @Min(value = 0, message = "测速模式值不能小于0")
    @Max(value = 1, message = "测速模式值不能大于1")
    private Integer mode;
}