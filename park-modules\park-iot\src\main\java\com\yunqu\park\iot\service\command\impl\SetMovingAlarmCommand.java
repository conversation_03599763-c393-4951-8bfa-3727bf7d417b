package com.yunqu.park.iot.service.command.impl;

import com.yunqu.park.iot.model.command.CommandType;
import com.yunqu.park.iot.model.command.dto.SetMovingAlarmParams;
import com.yunqu.park.iot.service.command.ICommand;
import lombok.RequiredArgsConstructor;

@RequiredArgsConstructor
public class SetMovingAlarmCommand implements ICommand {

    private final SetMovingAlarmParams params;
    private static final String TEMPLATE = "MOVING,%s,%d,%d#";

    @Override
    public CommandType getType() {
        return CommandType.SET_MOVING_ALARM;
    }

    @Override
    public String build() {
        return String.format(TEMPLATE, params.getState(), params.getRadius(), params.getMode());
    }

    @Override
    public String getDescription() {
        return String.format("%s: 状态=%s, 半径=%d米, 模式=%d",
            getType().getDescription(), params.getState(), params.getRadius(), params.getMode());
    }
}