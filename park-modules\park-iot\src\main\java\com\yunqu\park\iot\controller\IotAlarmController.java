package com.yunqu.park.iot.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.yunqu.park.common.core.domain.R;
import com.yunqu.park.common.core.validate.AddGroup;
import com.yunqu.park.common.core.validate.EditGroup;
import com.yunqu.park.common.excel.utils.ExcelUtil;
import com.yunqu.park.common.idempotent.annotation.RepeatSubmit;
import com.yunqu.park.common.log.annotation.Log;
import com.yunqu.park.common.log.enums.BusinessType;
import com.yunqu.park.common.mybatis.core.page.PageQuery;
import com.yunqu.park.common.mybatis.core.page.TableDataInfo;
import com.yunqu.park.common.satoken.utils.LoginHelper;
import com.yunqu.park.common.web.core.BaseController;
import com.yunqu.park.iot.domain.bo.IotAlarmRecordBo;
import com.yunqu.park.iot.domain.vo.IotAlarmRecordVo;
import com.yunqu.park.iot.service.IIotAlarmRecordService;
import lombok.RequiredArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import java.util.Date;
import java.util.List;

/**
 * 设备报警记录管理
 *
 * <AUTHOR>
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/iot/alarm")
public class IotAlarmController extends BaseController {

    private final IIotAlarmRecordService iotAlarmRecordService;

    /**
     * 查询设备报警记录列表
     */
    @SaCheckPermission("iot:alarm:list")
    @GetMapping("/list")
    public TableDataInfo<IotAlarmRecordVo> list(IotAlarmRecordBo bo, PageQuery pageQuery) {
        return iotAlarmRecordService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出设备报警记录列表
     */
    @SaCheckPermission("iot:alarm:export")
    @Log(title = "设备报警记录", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(IotAlarmRecordBo bo, HttpServletResponse response) {
        List<IotAlarmRecordVo> list = iotAlarmRecordService.queryList(bo);
        ExcelUtil.exportExcel(list, "设备报警记录", IotAlarmRecordVo.class, response);
    }

    /**
     * 获取设备报警记录详细信息
     */
    @SaCheckPermission("iot:alarm:query")
    @GetMapping("/{alarmId}")
    public R<IotAlarmRecordVo> getInfo(@NotNull(message = "主键不能为空")
                                       @PathVariable Long alarmId) {
        return R.ok(iotAlarmRecordService.queryById(alarmId));
    }

    /**
     * 新增设备报警记录
     */
    @SaCheckPermission("iot:alarm:add")
    @Log(title = "设备报警记录", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody IotAlarmRecordBo bo) {
        return toAjax(iotAlarmRecordService.insertByBo(bo));
    }

    /**
     * 修改设备报警记录
     */
    @SaCheckPermission("iot:alarm:edit")
    @Log(title = "设备报警记录", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody IotAlarmRecordBo bo) {
        return toAjax(iotAlarmRecordService.updateByBo(bo));
    }

    /**
     * 删除设备报警记录
     */
    @SaCheckPermission("iot:alarm:remove")
    @Log(title = "设备报警记录", businessType = BusinessType.DELETE)
    @DeleteMapping("/{alarmIds}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] alarmIds) {
        return toAjax(iotAlarmRecordService.deleteWithValidByIds(List.of(alarmIds), true));
    }

    /**
     * 处理报警记录
     */
    @SaCheckPermission("iot:alarm:handle")
    @Log(title = "处理报警记录", businessType = BusinessType.UPDATE)
    @PutMapping("/handle/{alarmId}")
    public R<Void> handleAlarm(@NotNull(message = "报警ID不能为空") @PathVariable Long alarmId,
                               @RequestParam(required = false) String handleRemark) {
        Long handleUser = LoginHelper.getUserId();
        return toAjax(iotAlarmRecordService.handleAlarm(alarmId, handleUser, handleRemark));
    }

    /**
     * 根据设备IMEI查询未处理报警
     */
    @SaCheckPermission("iot:alarm:query")
    @GetMapping("/unhandled/{imei}")
    public R<List<IotAlarmRecordVo>> getUnhandledAlarms(@NotBlank(message = "IMEI不能为空")
                                                        @PathVariable String imei) {
        return R.ok(iotAlarmRecordService.getUnhandledAlarms(imei));
    }

    /**
     * 获取报警统计信息
     */
    @SaCheckPermission("iot:alarm:list")
    @GetMapping("/statistics")
    public R<Object> getAlarmStatistics(
            @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date startTime,
            @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date endTime) {
        return R.ok(iotAlarmRecordService.getAlarmStatistics(startTime, endTime));
    }
}
