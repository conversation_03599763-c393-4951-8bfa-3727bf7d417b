package com.yunqu.park.iot.model.dto.command;

import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.Max;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 设置静止上传间隔请求
 * 
 * <AUTHOR>
 * @since 2025-01-06
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class SetStaticUploadIntervalRequest extends BaseCommandReq {
    
    /**
     * 静止上传间隔 (秒: 1-65535)
     */
    @NotNull(message = "静止上传间隔不能为空")
    @Min(value = 1, message = "静止上传间隔不能小于1秒")
    @Max(value = 65535, message = "静止上传间隔不能大于65535秒")
    private Integer interval;
}