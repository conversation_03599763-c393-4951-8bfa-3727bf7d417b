package com.yunqu.park.iot.service.command.impl;

import com.yunqu.park.iot.model.command.CommandType;
import com.yunqu.park.iot.model.command.dto.SetAutoArmParams;
import com.yunqu.park.iot.service.command.ICommand;
import lombok.RequiredArgsConstructor;

@RequiredArgsConstructor
public class Set<PERSON><PERSON>ArmCommand implements ICommand {

    private final SetAutoArmParams params;
    private static final String TEMPLATE = "DEFENSE,%d#";

    @Override
    public CommandType getType() {
        return CommandType.SET_AUTO_ARM;
    }

    @Override
    public String build() {
        return String.format(TEMPLATE, params.getDelayMinutes());
    }

    @Override
    public String getDescription() {
        return String.format("%s: 延迟 %d 分钟", getType().getDescription(), params.getDelayMinutes());
    }
}