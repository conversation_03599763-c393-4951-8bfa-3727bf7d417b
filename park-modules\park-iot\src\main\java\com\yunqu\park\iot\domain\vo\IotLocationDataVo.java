package com.yunqu.park.iot.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.yunqu.park.common.excel.annotation.ExcelDictFormat;
import com.yunqu.park.common.excel.convert.ExcelDictConvert;
import com.yunqu.park.iot.domain.IotLocationData;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 设备定位数据视图对象 iot_location_data
 *
 * <AUTHOR>
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = IotLocationData.class)
public class IotLocationDataVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 定位记录ID
     */
    @ExcelProperty(value = "定位记录ID")
    private Long locationId;

    /**
     * 设备ID
     */
    @ExcelProperty(value = "设备ID")
    private Long deviceId;

    /**
     * 设备IMEI
     */
    @ExcelProperty(value = "设备IMEI")
    private String imei;

    /**
     * GPS时间
     */
    @ExcelProperty(value = "GPS时间")
    private Date gpsTime;

    /**
     * 纬度
     */
    @ExcelProperty(value = "纬度")
    private BigDecimal latitude;

    /**
     * 经度
     */
    @ExcelProperty(value = "经度")
    private BigDecimal longitude;

    /**
     * 海拔高度(米)
     */
    @ExcelProperty(value = "海拔高度(米)")
    private Integer altitude;

    /**
     * 速度(km/h)
     */
    @ExcelProperty(value = "速度(km/h)")
    private Integer speed;

    /**
     * 方向角(0-360度)
     */
    @ExcelProperty(value = "方向角(度)")
    private Integer direction;

    /**
     * 卫星数量
     */
    @ExcelProperty(value = "卫星数量")
    private Integer satelliteCount;

    /**
     * GPS状态:0-未定位,1-已定位
     */
    @ExcelProperty(value = "GPS状态", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "iot_gps_status")
    private String gpsStatus;

    /**
     * ACC状态:0-关闭,1-开启
     */
    @ExcelProperty(value = "ACC状态", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "iot_acc_status")
    private String accStatus;

    /**
     * 移动国家代码
     */
    @ExcelProperty(value = "移动国家代码")
    private Integer mcc;

    /**
     * 移动网络代码
     */
    @ExcelProperty(value = "移动网络代码")
    private Integer mnc;

    /**
     * 位置区码
     */
    @ExcelProperty(value = "位置区码")
    private Integer lac;

    /**
     * 基站ID
     */
    @ExcelProperty(value = "基站ID")
    private Integer cellId;

    /**
     * 信号强度(0-100)
     */
    @ExcelProperty(value = "信号强度")
    private Integer signalStrength;

    /**
     * 里程(米)
     */
    @ExcelProperty(value = "里程(米)")
    private Integer mileage;

    /**
     * 租户编号
     */
    private String tenantId;

    /**
     * 创建时间
     */
    @ExcelProperty(value = "创建时间")
    private Date createTime;

}
