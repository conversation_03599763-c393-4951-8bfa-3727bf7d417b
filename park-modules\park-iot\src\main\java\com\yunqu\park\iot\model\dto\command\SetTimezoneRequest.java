package com.yunqu.park.iot.model.dto.command;

import com.yunqu.park.iot.model.command.dto.SetTimezoneParams;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 设置时区指令请求
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "设置时区指令请求")
public class SetTimezoneRequest extends BaseCommandReq {

    @Schema(description = "时区参数")
    private SetTimezoneParams params;
}