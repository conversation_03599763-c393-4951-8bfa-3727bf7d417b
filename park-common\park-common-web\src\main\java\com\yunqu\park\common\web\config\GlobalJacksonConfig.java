package com.yunqu.park.common.web.config;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.JsonDeserializer;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.module.SimpleModule;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.http.converter.json.Jackson2ObjectMapperBuilder;

import java.io.IOException;
import java.sql.Timestamp;
import java.util.Date;

/**
 * 全局Jackson配置
 * 自定义Date类型的序列化和反序列化
 *
 * <AUTHOR>
 */
@Slf4j
@Configuration
@ConditionalOnClass(ObjectMapper.class)
public class GlobalJacksonConfig {

    /**
     * 自定义java.util.Date反序列化器
     * 处理空字符串和各种日期格式
     */
    public static class CustomDateDeserializer extends JsonDeserializer<Date> {

        @Override
        public Date deserialize(JsonParser parser, DeserializationContext context) throws IOException {
            String dateString = parser.getText();
            
            try {
                // 如果是空字符串或null，返回null
                if (StrUtil.isBlank(dateString)) {
                    log.debug("Deserializing blank string to null Date");
                    return null;
                }

                // 去除前后空格
                dateString = dateString.trim();

                // 如果是空字符串，返回null
                if (dateString.isEmpty()) {
                    log.debug("Deserializing empty string to null Date");
                    return null;
                }

                // 特殊处理常见的无效值
                if ("null".equalsIgnoreCase(dateString) || "undefined".equalsIgnoreCase(dateString)) {
                    log.debug("Deserializing '{}' to null Date", dateString);
                    return null;
                }

                // 尝试解析时间戳（毫秒）
                if (dateString.matches("\\d{13}")) {
                    long timestamp = Long.parseLong(dateString);
                    log.debug("Deserializing timestamp {} to Date", timestamp);
                    return new Date(timestamp);
                }

                // 尝试解析时间戳（秒）
                if (dateString.matches("\\d{10}")) {
                    long timestamp = Long.parseLong(dateString) * 1000;
                    log.debug("Deserializing timestamp {} (seconds) to Date", dateString);
                    return new Date(timestamp);
                }

                // 使用hutool的DateUtil进行智能解析
                Date result = DateUtil.parse(dateString);
                log.debug("Successfully deserialized '{}' to Date: {}", dateString, result);
                return result;

            } catch (Exception e) {
                log.warn("Failed to deserialize date string '{}': {}, returning null", dateString, e.getMessage());
                return null;
            }
        }
    }

    /**
     * 自定义java.sql.Date反序列化器
     */
    public static class CustomSqlDateDeserializer extends JsonDeserializer<java.sql.Date> {

        @Override
        public java.sql.Date deserialize(JsonParser parser, DeserializationContext context) throws IOException {
            Date utilDate = new CustomDateDeserializer().deserialize(parser, context);
            return utilDate != null ? new java.sql.Date(utilDate.getTime()) : null;
        }
    }

    /**
     * 自定义java.sql.Timestamp反序列化器
     */
    public static class CustomTimestampDeserializer extends JsonDeserializer<Timestamp> {

        @Override
        public Timestamp deserialize(JsonParser parser, DeserializationContext context) throws IOException {
            Date utilDate = new CustomDateDeserializer().deserialize(parser, context);
            return utilDate != null ? new Timestamp(utilDate.getTime()) : null;
        }
    }

    /**
     * 配置全局ObjectMapper
     */
    @Bean
    @Primary
    public ObjectMapper globalObjectMapper(Jackson2ObjectMapperBuilder builder) {
        ObjectMapper objectMapper = builder.build();
        
        // 创建自定义模块
        SimpleModule globalDateModule = new SimpleModule("GlobalDateModule");
        
        // 注册自定义Date反序列化器
        globalDateModule.addDeserializer(Date.class, new CustomDateDeserializer());
        globalDateModule.addDeserializer(java.sql.Date.class, new CustomSqlDateDeserializer());
        globalDateModule.addDeserializer(Timestamp.class, new CustomTimestampDeserializer());
        
        // 注册模块
        objectMapper.registerModule(globalDateModule);
        
        log.info("Global Jackson date deserializers registered successfully:");
        log.info("  - java.util.Date deserializer");
        log.info("  - java.sql.Date deserializer");
        log.info("  - java.sql.Timestamp deserializer");
        
        return objectMapper;
    }
}
