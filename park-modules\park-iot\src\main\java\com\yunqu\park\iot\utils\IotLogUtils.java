package com.yunqu.park.iot.utils;

import com.yunqu.park.iot.constant.IotLogMarkers;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.concurrent.atomic.AtomicLong;

/**
 * IoT日志工具类
 * 提供统一的日志格式和统计功能
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class IotLogUtils {

    private static final DateTimeFormatter FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss.SSS");
    
    // 统计计数器
    private static final AtomicLong deviceConnectionCount = new AtomicLong(0);
    private static final AtomicLong deviceDisconnectionCount = new AtomicLong(0);
    private static final AtomicLong messageReceivedCount = new AtomicLong(0);
    private static final AtomicLong commandSentCount = new AtomicLong(0);
    private static final AtomicLong protocolErrorCount = new AtomicLong(0);

    /**
     * 记录设备连接日志
     */
    public static void logDeviceConnection(String imei, String remoteAddress, String channelId) {
        deviceConnectionCount.incrementAndGet();
        log.info(IotLogMarkers.IOT_CONNECTION,
                "[DEVICE-CONNECT] 🔗 Device connected: IMEI={}, RemoteAddress={}, ChannelId={}, TotalConnections={}",
                imei, remoteAddress, channelId, deviceConnectionCount.get());
    }

    /**
     * 记录设备断开连接日志
     */
    public static void logDeviceDisconnection(String imei, String remoteAddress, String channelId, String reason) {
        deviceDisconnectionCount.incrementAndGet();
        log.info(IotLogMarkers.IOT_CONNECTION,
                "[DEVICE-DISCONNECT] 🔌 Device disconnected: IMEI={}, RemoteAddress={}, ChannelId={}, Reason={}, TotalDisconnections={}",
                imei, remoteAddress, channelId, reason, deviceDisconnectionCount.get());
    }

    /**
     * 记录消息接收日志
     */
    public static void logMessageReceived(String imei, String protocol, int sequenceNumber, int contentLength) {
        messageReceivedCount.incrementAndGet();
        log.info(IotLogMarkers.IOT_PROTOCOL,
                "[MESSAGE-RECV] 📨 Message received: IMEI={}, Protocol={}, SequenceNumber={}, ContentLength={}, TotalMessages={}",
                imei, protocol, sequenceNumber, contentLength, messageReceivedCount.get());
    }

    /**
     * 记录指令发送日志
     */
    public static void logCommandSent(String imei, String commandType, String command, boolean success) {
        commandSentCount.incrementAndGet();
        String status = success ? "✅ SUCCESS" : "❌ FAILED";
        log.info(IotLogMarkers.IOT_COMMAND,
                "[COMMAND-SENT] {} Command sent: IMEI={}, Type={}, Command={}, TotalCommands={}",
                status, imei, commandType, command, commandSentCount.get());
    }

    /**
     * 记录协议错误日志
     */
    public static void logProtocolError(String imei, String error, String details) {
        protocolErrorCount.incrementAndGet();
        log.error("[PROTOCOL-ERROR] ❌ Protocol error: IMEI={}, Error={}, Details={}, TotalErrors={}", 
                 imei, error, details, protocolErrorCount.get());
    }

    /**
     * 记录设备状态变更日志
     */
    public static void logDeviceStatusChange(String imei, String oldStatus, String newStatus, String reason) {
        log.info("[DEVICE-STATUS] 🔄 Status changed: IMEI={}, OldStatus={}, NewStatus={}, Reason={}", 
                imei, oldStatus, newStatus, reason);
    }

    /**
     * 记录数据处理日志
     */
    public static void logDataProcessing(String imei, String dataType, boolean success, String details) {
        String status = success ? "✅ SUCCESS" : "❌ FAILED";
        log.info("[DATA-PROCESS] {} Data processing: IMEI={}, Type={}, Details={}", 
                status, imei, dataType, details);
    }

    /**
     * 记录性能监控日志
     */
    public static void logPerformanceMetrics(String operation, long duration, boolean success) {
        String status = success ? "✅" : "❌";
        log.info("[PERFORMANCE] {} Operation: {}, Duration={}ms, Status={}", 
                status, operation, duration, success ? "SUCCESS" : "FAILED");
    }

    /**
     * 记录业务操作日志
     */
    public static void logBusinessOperation(String imei, String operation, String details, boolean success) {
        String status = success ? "✅ SUCCESS" : "❌ FAILED";
        log.info("[BUSINESS-OP] {} IMEI={}, Operation={}, Details={}", 
                status, imei, operation, details);
    }

    /**
     * 获取统计信息
     */
    public static String getStatistics() {
        return String.format(
                "[IOT-STATISTICS] Connections=%d, Disconnections=%d, Messages=%d, Commands=%d, Errors=%d, Time=%s",
                deviceConnectionCount.get(),
                deviceDisconnectionCount.get(),
                messageReceivedCount.get(),
                commandSentCount.get(),
                protocolErrorCount.get(),
                LocalDateTime.now().format(FORMATTER)
        );
    }

    /**
     * 重置统计计数器
     */
    public static void resetStatistics() {
        deviceConnectionCount.set(0);
        deviceDisconnectionCount.set(0);
        messageReceivedCount.set(0);
        commandSentCount.set(0);
        protocolErrorCount.set(0);
        log.info("[IOT-STATISTICS] Statistics counters reset");
    }

    /**
     * 定期输出统计信息
     */
    public static void logStatistics() {
        log.info(getStatistics());
    }

    /**
     * 格式化字节数组为十六进制字符串
     */
    public static String formatHexString(byte[] data) {
        if (data == null || data.length == 0) {
            return "";
        }
        StringBuilder sb = new StringBuilder();
        for (byte b : data) {
            sb.append(String.format("%02X ", b));
        }
        return sb.toString().trim();
    }

    /**
     * 记录原始数据日志（用于调试）
     */
    public static void logRawData(String imei, String direction, byte[] data) {
        if (log.isDebugEnabled()) {
            log.debug("[RAW-DATA] {} IMEI={}, Length={}, Data=[{}]", 
                     direction, imei, data.length, formatHexString(data));
        }
    }

    /**
     * 记录心跳日志
     */
    public static void logHeartbeat(String imei, int sequenceNumber) {
        log.debug("[HEARTBEAT] 💓 IMEI={}, SequenceNumber={}, Time={}", 
                 imei, sequenceNumber, LocalDateTime.now().format(FORMATTER));
    }

    /**
     * 记录位置数据日志
     */
    public static void logLocationData(String imei, double latitude, double longitude, int speed, String time) {
        log.info("[LOCATION] 📍 IMEI={}, Lat={}, Lng={}, Speed={}km/h, Time={}", 
                imei, latitude, longitude, speed, time);
    }

    /**
     * 记录报警日志
     */
    public static void logAlarm(String imei, String alarmType, String description, String level) {
        log.warn("[ALARM] 🚨 IMEI={}, Type={}, Level={}, Description={}", 
                imei, alarmType, level, description);
    }
}
