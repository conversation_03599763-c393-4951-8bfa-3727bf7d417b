package com.yunqu.park.iot.service.command.impl;

import com.yunqu.park.iot.model.command.CommandType;
import com.yunqu.park.iot.model.command.dto.SetDrivingBehaviorParams;
import com.yunqu.park.iot.service.command.ICommand;
import lombok.RequiredArgsConstructor;

@RequiredArgsConstructor
public class SetHarshTurningAlarmCommand implements ICommand {

    private final SetDrivingBehaviorParams params;
    private static final String TEMPLATE = "EANGLE,VALUE,%d,%d#";

    @Override
    public CommandType getType() {
        return CommandType.SET_HARSH_TURNING_ALARM;
    }

    @Override
    public String build() {
        return String.format(TEMPLATE, params.getValue(), params.getMode());
    }

    @Override
    public String getDescription() {
        return String.format("%s: 阈值=%d, 模式=%d", getType().getDescription(), params.getValue(), params.getMode());
    }
}