package com.yunqu.park.iot.model.command.dto;

import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = false)
public class SetTimezoneParams extends CommandParams {
    @NotBlank(message = "时区方向不能为空 (E/W)")
    private String direction;

    @NotNull(message = "小时偏移不能为空")
    private Integer hours;

    @NotNull(message = "分钟偏移不能为空")
    @Min(0)
    @Max(59)
    private Integer minutes;
}