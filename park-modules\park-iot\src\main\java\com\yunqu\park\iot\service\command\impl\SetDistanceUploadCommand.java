package com.yunqu.park.iot.service.command.impl;

import com.yunqu.park.iot.model.command.CommandType;
import com.yunqu.park.iot.model.command.dto.SetDistanceUploadParams;
import com.yunqu.park.iot.service.command.ICommand;
import lombok.RequiredArgsConstructor;

@RequiredArgsConstructor
public class SetDistanceUploadCommand implements ICommand {

    private final SetDistanceUploadParams params;
    private static final String TEMPLATE = "DISTANCE,%d#";

    @Override
    public CommandType getType() {
        return CommandType.SET_DISTANCE_UPLOAD;
    }

    @Override
    public String build() {
        return String.format(TEMPLATE, params.getDistance());
    }

    @Override
    public String getDescription() {
        return String.format("%s: %d米", getType().getDescription(), params.getDistance());
    }
}