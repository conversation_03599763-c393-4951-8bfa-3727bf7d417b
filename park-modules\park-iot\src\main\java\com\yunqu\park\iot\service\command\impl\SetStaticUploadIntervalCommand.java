package com.yunqu.park.iot.service.command.impl;

import com.yunqu.park.iot.model.command.CommandType;
import com.yunqu.park.iot.model.command.dto.SetStaticUploadIntervalParams;
import com.yunqu.park.iot.service.command.ICommand;
import lombok.RequiredArgsConstructor;

@RequiredArgsConstructor
public class SetStaticUploadIntervalCommand implements ICommand {

    private final SetStaticUploadIntervalParams params;
    private static final String TEMPLATE = "STATIC,%d#";

    @Override
    public CommandType getType() {
        return CommandType.SET_STATIC_UPLOAD_INTERVAL;
    }

    @Override
    public String build() {
        return String.format(TEMPLATE, params.getInterval());
    }

    @Override
    public String getDescription() {
        return String.format("%s: %d秒", getType().getDescription(), params.getInterval());
    }
}