package com.yunqu.park.iot.service.command.impl;

import com.yunqu.park.iot.model.command.CommandType;
import com.yunqu.park.iot.model.command.dto.SetMileageStatsParams;
import com.yunqu.park.iot.service.command.ICommand;
import lombok.RequiredArgsConstructor;

@RequiredArgsConstructor
public class SetMileageStatsCommand implements ICommand {

    private final SetMileageStatsParams params;
    private static final String TEMPLATE = "MILEAGE,%d#";

    @Override
    public CommandType getType() {
        return CommandType.SET_MILEAGE_STATISTICS;
    }

    @Override
    public String build() {
        return String.format(TEMPLATE, params.getState());
    }

    @Override
    public String getDescription() {
        return String.format("%s: 状态=%s", getType().getDescription(), params.getState() == 1 ? "开启" : "关闭");
    }
}