package com.yunqu.park.iot.service.command.impl;

import com.yunqu.park.iot.model.command.CommandType;
import com.yunqu.park.iot.model.command.dto.SetHeartbeatIntervalParams;
import com.yunqu.park.iot.service.command.ICommand;
import lombok.RequiredArgsConstructor;

@RequiredArgsConstructor
public class SetHeartbeatIntervalCommand implements ICommand {

    private final SetHeartbeatIntervalParams params;
    private static final String TEMPLATE = "HBT,%d#";

    @Override
    public CommandType getType() {
        return CommandType.SET_HEARTBEAT_INTERVAL;
    }

    @Override
    public String build() {
        return String.format(TEMPLATE, params.getInterval());
    }

    @Override
    public String getDescription() {
        return String.format("%s: %d秒", getType().getDescription(), params.getInterval());
    }
}