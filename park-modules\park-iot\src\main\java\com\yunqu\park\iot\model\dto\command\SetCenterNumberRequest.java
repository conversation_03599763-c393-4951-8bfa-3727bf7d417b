package com.yunqu.park.iot.model.dto.command;

import com.yunqu.park.iot.model.command.dto.SetCenterNumberParams;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 设置中心号码指令请求
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "设置中心号码指令请求")
public class SetCenterNumberRequest extends BaseCommandReq {

    @Schema(description = "中心号码参数")
    private SetCenterNumberParams params;
}