package com.yunqu.park.iot.service.command.impl;

import com.yunqu.park.iot.model.command.CommandType;
import com.yunqu.park.iot.service.command.ICommand;
import lombok.RequiredArgsConstructor;

@RequiredArgsConstructor
public class QueryNetworkCommand implements ICommand {

    private static final String TEMPLATE = "NETWORK#";

    @Override
    public CommandType getType() {
        return CommandType.QUERY_NETWORK;
    }

    @Override
    public String build() {
        return TEMPLATE;
    }

    @Override
    public String getDescription() {
        return getType().getDescription();
    }
}