package com.yunqu.park.iot.service.command;

import com.yunqu.park.iot.model.command.CommandType;
import com.yunqu.park.iot.model.command.dto.*;
import com.yunqu.park.iot.service.command.impl.*;
import org.springframework.stereotype.Component;

/**
 * IoT设备指令工厂类
 *
 * <p>采用工厂模式和策略模式的核心组件，负责根据指令类型动态创建对应的指令策略对象。
 * 该工厂类是整个指令系统的核心，将指令类型与具体实现解耦，支持54种不同的IoT设备指令。</p>
 *
 * <h3>设计模式：</h3>
 * <ul>
 *   <li><strong>工厂模式</strong>：根据CommandType枚举创建对应的ICommand实现</li>
 *   <li><strong>策略模式</strong>：每个ICommand实现都是一个独立的指令策略</li>
 *   <li><strong>类型安全</strong>：编译时确保参数类型与指令类型的匹配</li>
 * </ul>
 *
 * <h3>支持的指令分类：</h3>
 * <ul>
 *   <li><strong>无参数指令</strong>（13个）：
 *       <ul>
 *         <li>基础操作：REBOOT, FACTORY_RESET, CLEAR_DATA, CLEAR_MILEAGE</li>
 *         <li>查询操作：QUERY_LOCATION, QUERY_STATUS, QUERY_VERSION等</li>
 *         <li>状态设置：SET_DISARM_STATE, DELETE_CENTER_NUMBER</li>
 *       </ul>
 *   </li>
 *   <li><strong>有参数指令</strong>（41个）：
 *       <ul>
 *         <li>网络配置：SET_IP, SET_DUAL_IP, SET_APN等</li>
 *         <li>报警设置：SET_SPEED_ALARM, SET_MOVING_ALARM等</li>
 *         <li>功能配置：SET_GPS_MODE, SET_MILEAGE_STATISTICS等</li>
 *       </ul>
 *   </li>
 * </ul>
 *
 * <h3>参数类型映射：</h3>
 * <pre>{@code
 * // 无参数指令使用NoParams
 * REBOOT -> new RebootCommand()
 *
 * // 有参数指令使用对应的Params类
 * SET_IP -> new SetIpCommand((SetIpParams) params)
 * SET_SPEED_ALARM -> new SetSpeedAlarmCommand((SetSpeedAlarmParams) params)
 * }</pre>
 *
 * <h3>扩展指南：</h3>
 * <ol>
 *   <li>在{@link com.yunqu.park.iot.model.command.CommandType}中添加新的指令类型</li>
 *   <li>创建对应的参数类（继承{@link com.yunqu.park.iot.model.command.dto.CommandParams}）</li>
 *   <li>实现{@link ICommand}接口创建指令策略类</li>
 *   <li>在本工厂类的switch语句中添加新的case分支</li>
 *   <li>更新{@link com.yunqu.park.iot.model.command.CommandRequest}的JsonSubTypes注解</li>
 * </ol>
 *
 * <h3>异常处理：</h3>
 * <ul>
 *   <li><strong>不支持的指令类型</strong>：抛出IllegalArgumentException</li>
 *   <li><strong>参数类型不匹配</strong>：在运行时抛出ClassCastException</li>
 *   <li><strong>空参数</strong>：由各个Command实现类自行处理</li>
 * </ul>
 *
 * <h3>性能特点：</h3>
 * <ul>
 *   <li><strong>零反射</strong>：使用switch表达式，编译时优化</li>
 *   <li><strong>类型安全</strong>：编译时检查参数类型匹配</li>
 *   <li><strong>快速创建</strong>：直接构造函数调用，无额外开销</li>
 * </ul>
 *
 * <AUTHOR>
 * @version 2.0
 * @since 2025-01-06
 * @see ICommand
 * @see com.yunqu.park.iot.model.command.CommandType
 * @see com.yunqu.park.iot.model.command.dto.CommandParams
 */
@Component
public class CommandFactory {

    /**
     * 创建指令策略对象.
     *
     * @param type   指令类型
     * @param params 指令参数 (可能为NoParams的实例)
     * @return 对应的ICommand实现
     * @throws IllegalArgumentException 如果指令类型不被支持
     */
    public ICommand createCommand(CommandType type, CommandParams params) {
        return switch (type) {
            // --- 无参数指令 ---
            case REBOOT -> new RebootCommand();
            case FACTORY_RESET -> new FactoryResetCommand();
            case QUERY_LOCATION -> new QueryLocationCommand();
            case QUERY_LINK_ADDRESS -> new QueryLinkAddressCommand();
            case QUERY_PARAMETERS -> new QueryParametersCommand();
            case QUERY_STATUS -> new QueryStatusCommand();
            case QUERY_VERSION -> new QueryVersionCommand();
            case QUERY_ICCID -> new QueryIccidCommand();
            case QUERY_NETWORK -> new QueryNetworkCommand();
            case QUERY_MODULE_VERSION -> new QueryModuleVersionCommand();
            case QUERY_VIBRATION_SENSITIVITY -> new QueryVibrationSensitivityCommand();
            case QUERY_MILEAGE_STATISTICS -> new QueryMileageStatisticsCommand();
            case QUERY_NETWORK_MODE -> new QueryNetworkModeCommand();
            case CLEAR_MILEAGE -> new ClearMileageCommand();
            case DELETE_CENTER_NUMBER -> new DeleteCenterNumberCommand();
            case SET_DISARM_STATE -> new SetDisarmStateCommand();
            case CLEAR_DATA -> new ClearDataCommand();

            // --- 有参数指令 ---
            case SET_IP -> new SetIpCommand((SetIpParams) params);
            case SET_DUAL_IP -> new SetDualIpCommand((SetDualIpParams) params);
            case SET_APN -> new SetApnCommand((SetApnParams) params);
            case SET_UPLOAD_INTERVAL -> new SetUploadIntervalCommand((SetUploadIntervalParams) params);
            case SET_HEARTBEAT_INTERVAL -> new SetHeartbeatIntervalCommand((SetHeartbeatIntervalParams) params);
            case SET_SOS_NUMBER -> new SetSosNumberCommand((SetSosNumberParams) params);
            case DELETE_SOS_NUMBER -> new DeleteSosNumberCommand((DeleteSosNumberParams) params);
            case SET_CENTER_NUMBER -> new SetCenterNumberCommand((SetCenterNumberParams) params);
            case CONTROL_RELAY -> new ControlRelayCommand((ControlRelayParams) params);
            case SET_TIMEZONE -> new SetTimezoneCommand((SetTimezoneParams) params);
            case SET_LANGUAGE -> new SetLanguageCommand((SetLanguageParams) params);
            case SET_AUTO_ARM -> new SetAutoArmCommand((SetAutoArmParams) params);
            case SET_ANGLE_UPLOAD -> new SetAngleUploadCommand((SetAngleUploadParams) params);
            case SET_ANGLE_VALUE -> new SetAngleValueCommand((SetAngleValueParams) params);
            case SET_UPLOAD_TIMEZONE -> new SetUploadTimezoneCommand((SetUploadTimezoneParams) params);
            case SET_VIBRATION_SENSITIVITY -> new SetVibrationSensitivityCommand((SetVibrationSensitivityParams) params);
            case SET_POWER_ALARM -> new SetPowerAlarmCommand((SetAlarmParams) params);
            case SET_ACC_ALARM -> new SetAccAlarmCommand((SetAlarmParams) params);
            case SET_VIBRATION_ALARM -> new SetVibrationAlarmCommand((SetAlarmParams) params);
            case SET_LOW_BATTERY_ALARM -> new SetLowBatteryAlarmCommand((SetAlarmParams) params);
            case SET_SPEED_ALARM -> new SetSpeedAlarmCommand((SetSpeedAlarmParams) params);
            case SET_MOVING_ALARM -> new SetMovingAlarmCommand((SetMovingAlarmParams) params);
            case SET_HARSH_ACCELERATION_ALARM -> new SetHarshAccelerationAlarmCommand((SetDrivingBehaviorParams) params);
            case SET_HARSH_BRAKING_ALARM -> new SetHarshBrakingAlarmCommand((SetDrivingBehaviorParams) params);
            case SET_HARSH_TURNING_ALARM -> new SetHarshTurningAlarmCommand((SetDrivingBehaviorParams) params);
            case SET_COLLISION_ALARM -> new SetCollisionAlarmCommand((SetDrivingBehaviorParams) params);
            case SWITCH_MILEAGE_MODE -> new SwitchMileageModeCommand((SwitchMileageModeParams) params);
            case SET_MILEAGE_STATISTICS -> new SetMileageStatsCommand((SetMileageStatsParams) params);
            case SET_STATIC_SLEEP -> new SetStaticSleepCommand((SetStaticSleepParams) params);
            case SET_SPEED_MEASUREMENT_MODE -> new SetSpeedMeasurementModeCommand((SetSpeedMeasurementModeParams) params);
            case SET_GPS_MODE -> new SetGpsModeCommand((SetGpsModeParams) params);
            case SET_GPS_UPLOAD_DURATION -> new SetGpsUploadDurationCommand((SetGpsUploadDurationParams) params);
            case SET_GPRS_SWITCH -> new SetGprsSwitchCommand((SetGprsSwitchParams) params);
            case SET_SATELLITE_LOCK_SWITCH -> new SetSatelliteLockSwitchCommand((SetSatelliteLockSwitchParams) params);
            case SET_DISTANCE_UPLOAD -> new SetDistanceUploadCommand((SetDistanceUploadParams) params);
            case SET_GPS_SLEEP_WORK -> new SetGpsSleepWorkCommand((SetGpsSleepWorkParams) params);
            case SET_STATIC_UPLOAD_INTERVAL -> new SetStaticUploadIntervalCommand((SetStaticUploadIntervalParams) params);
            
            default -> throw new IllegalArgumentException("不支持的指令类型: " + type);
        };
    }
}