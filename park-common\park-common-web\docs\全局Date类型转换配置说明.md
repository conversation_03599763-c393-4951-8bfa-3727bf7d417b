# 全局Date类型转换配置说明

## 问题描述

在Spring Boot项目中，当前端传递空字符串给Date类型字段时，会出现以下错误：

```
Failed to convert property value of type 'java.lang.String' to required type 'java.util.Date' for property 'createTime'
Failed to convert from type [java.lang.String] to type [@com.baomidou.mybatisplus.annotation.TableField java.util.Date] for value []
```

## 解决方案

通过在`park-common-web`模块中创建全局的Date类型转换器来解决这个问题。

## 配置文件结构

```
park-common/park-common-web/src/main/java/com/yunqu/park/common/web/
├── config/
│   ├── GlobalDateConverterConfig.java           # 全局Date转换器
│   ├── GlobalWebMvcConfig.java                  # Web MVC配置
│   ├── GlobalJacksonConfig.java                 # Jackson配置
│   └── GlobalDateConversionAutoConfiguration.java # 自动配置
└── exception/
    └── GlobalTypeConversionExceptionHandler.java # 全局异常处理
```

## 核心功能

### 1. 类型转换器

支持以下转换：
- `String` → `java.util.Date`
- `String` → `java.sql.Date`
- `String` → `java.sql.Timestamp`

### 2. 支持的输入格式

- **空值处理**：`""`, `" "`, `null`, `"null"`, `"undefined"` → `null`
- **时间戳**：`1640995200000` (毫秒), `1640995200` (秒)
- **标准格式**：`yyyy-MM-dd`, `yyyy-MM-dd HH:mm:ss`
- **其他格式**：`yyyy/MM/dd`, `dd-MM-yyyy` 等（通过hutool智能解析）

### 3. 处理场景

- **GET请求参数**：`?createTime=&updateTime=2024-01-01`
- **POST请求体**：JSON中的Date字段
- **表单提交**：form-data中的Date字段

## 使用示例

### 前端请求示例

```javascript
// GET请求 - 空字符串会被转换为null
fetch('/api/devices?createTime=&updateTime=2024-01-01 10:30:00')

// POST请求 - JSON中的空字符串会被转换为null
fetch('/api/devices', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    imei: '123456789',
    createTime: '',           // 转换为 null
    updateTime: '2024-01-01', // 转换为 Date对象
    lastOnlineTime: 1640995200000 // 时间戳转换为 Date对象
  })
})
```

### 后端实体类

```java
public class IotDevice extends BaseEntity {
    private String imei;
    private Date createTime;    // 自动处理空字符串
    private Date updateTime;    // 自动处理空字符串
    private Date lastOnlineTime; // 自动处理空字符串
    // ...
}
```

## 测试接口

提供了测试接口来验证转换功能：

```bash
# 测试GET请求参数转换
GET /iot/test/date/test-params?createTime=&updateTime=2024-01-01

# 测试POST请求体转换
POST /iot/test/date/test-body
Content-Type: application/json
{
  "createTime": "",
  "updateTime": "2024-01-01 10:30:00",
  "lastOnlineTime": 1640995200000
}

# 测试各种日期格式
POST /iot/test/date/test-formats
Content-Type: application/json
{
  "format1": "2024-01-01",
  "format2": "2024-01-01 10:30:00",
  "format3": "1640995200000",
  "format4": "",
  "format5": "null"
}

# 测试空值处理
POST /iot/test/date/test-empty-values
```

## 异常处理

当日期转换失败时，会返回友好的错误信息：

```json
{
  "code": 400,
  "msg": "日期字段 'createTime' 的值 'invalid-date' 格式不正确，请使用正确的日期格式（如：yyyy-MM-dd 或 yyyy-MM-dd HH:mm:ss）",
  "data": null
}
```

## 自动配置

配置会自动生效，无需手动配置。通过Spring Boot的自动配置机制：

1. `GlobalDateConversionAutoConfiguration` 自动配置类
2. `META-INF/spring/org.springframework.boot.autoconfigure.AutoConfiguration.imports` 自动导入
3. 在Web应用启动时自动注册转换器

## 日志输出

启动时会输出配置信息：

```
INFO  GlobalWebMvcConfig - Global date converters registered successfully:
INFO  GlobalWebMvcConfig -   - String to java.util.Date
INFO  GlobalWebMvcConfig -   - String to java.sql.Date
INFO  GlobalWebMvcConfig -   - String to java.sql.Timestamp
INFO  GlobalJacksonConfig - Global Jackson date deserializers registered successfully:
INFO  GlobalJacksonConfig -   - java.util.Date deserializer
INFO  GlobalJacksonConfig -   - java.sql.Date deserializer
INFO  GlobalJacksonConfig -   - java.sql.Timestamp deserializer
```

## 注意事项

1. **优先级**：全局配置优先级高于模块特定配置
2. **性能**：转换器会缓存解析结果，性能影响很小
3. **兼容性**：兼容现有的日期格式，不会影响正常的日期处理
4. **扩展性**：可以通过修改转换器来支持更多日期格式
5. **调试**：开启DEBUG日志可以看到详细的转换过程

## 故障排除

如果转换仍然失败，请检查：

1. 确认`park-common-web`模块已正确引入
2. 检查启动日志中是否有转换器注册信息
3. 使用测试接口验证转换功能
4. 检查是否有其他配置覆盖了全局配置
