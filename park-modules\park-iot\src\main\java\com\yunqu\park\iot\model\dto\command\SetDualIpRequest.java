package com.yunqu.park.iot.model.dto.command;

import com.yunqu.park.iot.model.command.dto.SetDualIpParams;
import lombok.Data;
import lombok.EqualsAndHashCode;

import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;

/**
 * 设置双IP指令请求
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class SetDualIpRequest extends BaseCommandReq {

    /**
     * 双IP设置参数
     */
    @Valid
    @NotNull(message = "双IP参数不能为空")
    private SetDualIpParams params;
}