package com.yunqu.park.iot.service.command.impl;

import com.yunqu.park.iot.model.command.CommandType;
import com.yunqu.park.iot.model.command.dto.SetGpsUploadDurationParams;
import com.yunqu.park.iot.service.command.ICommand;
import lombok.RequiredArgsConstructor;

@RequiredArgsConstructor
public class SetGpsUploadDurationCommand implements ICommand {

    private final SetGpsUploadDurationParams params;
    private static final String TEMPLATE = "SEND,%d#";

    @Override
    public CommandType getType() {
        return CommandType.SET_GPS_UPLOAD_DURATION;
    }

    @Override
    public String build() {
        return String.format(TEMPLATE, params.getDuration());
    }

    @Override
    public String getDescription() {
        return String.format("%s: %d秒", getType().getDescription(), params.getDuration());
    }
}