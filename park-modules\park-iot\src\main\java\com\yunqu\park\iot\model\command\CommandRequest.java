package com.yunqu.park.iot.model.command;

import com.fasterxml.jackson.annotation.JsonSubTypes;
import com.fasterxml.jackson.annotation.JsonTypeInfo;
import com.yunqu.park.iot.model.command.dto.*;
import lombok.Data;

import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;

/**
 * 统一指令请求体 DTO.
 * 用于封装所有发送到设备的指令请求.
 * 使用了Jackson的多态反序列化, 允许根据 `commandType` 字段动态地将 `params` 字段反序列化为正确的DTO类型.
 */
@Data
public class CommandRequest {

    /**
     * 设备IMEI号.
     */
    @NotBlank(message = "设备IMEI不能为空")
    private String imei;

    /**
     * 指令类型, 必须是 CommandType 枚举中的一个有效值.
     */
    @NotNull(message = "指令类型不能为空")
    private CommandType commandType;

    /**
     * 指令参数, 其具体类型由 commandType 决定.
     * 使用 @JsonTypeInfo 和 @JsonSubTypes 来实现多态反序列化.
     */
    @NotNull(message = "指令参数不能为空")
    @Valid // 级联校验, 会校验 params 对象内部的字段
    @JsonTypeInfo(
            use = JsonTypeInfo.Id.NAME,
            include = JsonTypeInfo.As.EXTERNAL_PROPERTY,
            property = "commandType"
    )
    @JsonSubTypes({
            // --- 无参数指令 ---
            @JsonSubTypes.Type(value = NoParams.class, name = "REBOOT"),
            @JsonSubTypes.Type(value = NoParams.class, name = "FACTORY_RESET"),
            @JsonSubTypes.Type(value = NoParams.class, name = "QUERY_LOCATION"),
            @JsonSubTypes.Type(value = NoParams.class, name = "QUERY_LINK_ADDRESS"),
            @JsonSubTypes.Type(value = NoParams.class, name = "QUERY_PARAMETERS"),
            @JsonSubTypes.Type(value = NoParams.class, name = "QUERY_STATUS"),
            @JsonSubTypes.Type(value = NoParams.class, name = "QUERY_VERSION"),
            @JsonSubTypes.Type(value = NoParams.class, name = "QUERY_ICCID"),
            @JsonSubTypes.Type(value = NoParams.class, name = "QUERY_NETWORK"),
            @JsonSubTypes.Type(value = NoParams.class, name = "QUERY_MODULE_VERSION"),
            @JsonSubTypes.Type(value = NoParams.class, name = "QUERY_VIBRATION_SENSITIVITY"),
            @JsonSubTypes.Type(value = NoParams.class, name = "QUERY_MILEAGE_STATISTICS"),
            @JsonSubTypes.Type(value = NoParams.class, name = "QUERY_NETWORK_MODE"),
            @JsonSubTypes.Type(value = NoParams.class, name = "CLEAR_MILEAGE"),
            @JsonSubTypes.Type(value = NoParams.class, name = "DELETE_CENTER_NUMBER"),
            @JsonSubTypes.Type(value = NoParams.class, name = "SET_DISARM_STATE"),
            @JsonSubTypes.Type(value = NoParams.class, name = "CLEAR_DATA"),

            // --- 有参数指令 ---
            @JsonSubTypes.Type(value = SetIpParams.class, name = "SET_IP"),
            @JsonSubTypes.Type(value = SetDualIpParams.class, name = "SET_DUAL_IP"),
            @JsonSubTypes.Type(value = SetApnParams.class, name = "SET_APN"),
            @JsonSubTypes.Type(value = SetUploadIntervalParams.class, name = "SET_UPLOAD_INTERVAL"),
            @JsonSubTypes.Type(value = SetHeartbeatIntervalParams.class, name = "SET_HEARTBEAT_INTERVAL"),
            @JsonSubTypes.Type(value = SetSosNumberParams.class, name = "SET_SOS_NUMBER"),
            @JsonSubTypes.Type(value = DeleteSosNumberParams.class, name = "DELETE_SOS_NUMBER"),
            @JsonSubTypes.Type(value = SetCenterNumberParams.class, name = "SET_CENTER_NUMBER"),
            @JsonSubTypes.Type(value = ControlRelayParams.class, name = "CONTROL_RELAY"),
            @JsonSubTypes.Type(value = SetTimezoneParams.class, name = "SET_TIMEZONE"),
            @JsonSubTypes.Type(value = SetLanguageParams.class, name = "SET_LANGUAGE"),
            @JsonSubTypes.Type(value = SetAutoArmParams.class, name = "SET_AUTO_ARM"),
            @JsonSubTypes.Type(value = SetAngleUploadParams.class, name = "SET_ANGLE_UPLOAD"),
            @JsonSubTypes.Type(value = SetAngleValueParams.class, name = "SET_ANGLE_VALUE"),
            @JsonSubTypes.Type(value = SetUploadTimezoneParams.class, name = "SET_UPLOAD_TIMEZONE"),
            @JsonSubTypes.Type(value = SetVibrationSensitivityParams.class, name = "SET_VIBRATION_SENSITIVITY"),
            @JsonSubTypes.Type(value = SetAlarmParams.class, name = "SET_VIBRATION_ALARM"),
            @JsonSubTypes.Type(value = SetAlarmParams.class, name = "SET_POWER_ALARM"),
            @JsonSubTypes.Type(value = SetAlarmParams.class, name = "SET_LOW_BATTERY_ALARM"),
            @JsonSubTypes.Type(value = SetSpeedAlarmParams.class, name = "SET_SPEED_ALARM"),
            @JsonSubTypes.Type(value = SetMovingAlarmParams.class, name = "SET_MOVING_ALARM"),
            @JsonSubTypes.Type(value = SetAlarmParams.class, name = "SET_ACC_ALARM"),
            @JsonSubTypes.Type(value = SetDrivingBehaviorParams.class, name = "SET_HARSH_ACCELERATION_ALARM"),
            @JsonSubTypes.Type(value = SetDrivingBehaviorParams.class, name = "SET_HARSH_BRAKING_ALARM"),
            @JsonSubTypes.Type(value = SetDrivingBehaviorParams.class, name = "SET_HARSH_TURNING_ALARM"),
            @JsonSubTypes.Type(value = SetDrivingBehaviorParams.class, name = "SET_COLLISION_ALARM"),
            @JsonSubTypes.Type(value = SwitchMileageModeParams.class, name = "SWITCH_MILEAGE_MODE"),
            @JsonSubTypes.Type(value = SetMileageStatsParams.class, name = "SET_MILEAGE_STATISTICS"),
            @JsonSubTypes.Type(value = SetStaticSleepParams.class, name = "SET_STATIC_SLEEP"),
            @JsonSubTypes.Type(value = SetSpeedMeasurementModeParams.class, name = "SET_SPEED_MEASUREMENT_MODE"),
            @JsonSubTypes.Type(value = SetGpsModeParams.class, name = "SET_GPS_MODE"),
            @JsonSubTypes.Type(value = SetGpsUploadDurationParams.class, name = "SET_GPS_UPLOAD_DURATION"),
            @JsonSubTypes.Type(value = SetGprsSwitchParams.class, name = "SET_GPRS_SWITCH"),
            @JsonSubTypes.Type(value = SetSatelliteLockSwitchParams.class, name = "SET_SATELLITE_LOCK_SWITCH"),
            @JsonSubTypes.Type(value = SetDistanceUploadParams.class, name = "SET_DISTANCE_UPLOAD"),
            @JsonSubTypes.Type(value = SetGpsSleepWorkParams.class, name = "SET_GPS_SLEEP_WORK"),
            @JsonSubTypes.Type(value = SetStaticUploadIntervalParams.class, name = "SET_STATIC_UPLOAD_INTERVAL")
    })
    private CommandParams params;
}