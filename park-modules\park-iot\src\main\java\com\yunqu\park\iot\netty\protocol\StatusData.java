package com.yunqu.park.iot.netty.protocol;

import lombok.Data;
import lombok.Builder;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

/**
 * 终端状态信息模型
 * 用于封装从协议号 0x13, 0x23 等包中解析出的状态信息
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class StatusData {

    /**
     * 终端信息内容原始值
     */
    private int terminalInfo;

    /**
     * 电压等级 (0-6)
     */
    private int voltageLevel;

    /**
     * GSM信号强度 (0-4)
     */
    private int gsmSignalStrength;

    /**
     * 语言/扩展端口状态
     */
    private int language;

    // 便捷方法从 terminalInfo 解析具体字段

    public static StatusData from(byte terminalInfoByte) {
        int info = terminalInfoByte & 0xFF;
        return StatusData.builder()
            .terminalInfo(info)
            .voltageLevel((info >> 4) & 0x0F) // 示例：高4位为电压
            .gsmSignalStrength(info & 0x0F)   // 示例：低4位为信号
            .build();
    }

    public boolean isOilAndElectricityConnected() {
        return (terminalInfo & 0x01) == 1;
    }

    public boolean isGpsPositioning() {
        return (terminalInfo & 0x02) == 2;
    }

    public boolean isCharging() {
        return (terminalInfo & 0x20) == 0x20;
    }

    public boolean isAccHigh() {
        return (terminalInfo & 0x40) == 0x40;
    }

    public boolean isArmed() {
        return (terminalInfo & 0x80) == 0x80;
    }
}