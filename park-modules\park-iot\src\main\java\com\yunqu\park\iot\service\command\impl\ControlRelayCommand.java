package com.yunqu.park.iot.service.command.impl;

import com.yunqu.park.iot.model.command.CommandType;
import com.yunqu.park.iot.model.command.dto.ControlRelayParams;
import com.yunqu.park.iot.service.command.ICommand;
import lombok.RequiredArgsConstructor;

@RequiredArgsConstructor
public class ControlRelayCommand implements ICommand {

    private final ControlRelayParams params;
    private static final String TEMPLATE = "RELAY,%d#";

    @Override
    public CommandType getType() {
        return CommandType.CONTROL_RELAY;
    }

    @Override
    public String build() {
        return String.format(TEMPLATE, params.getState());
    }

    @Override
    public String getDescription() {
        return String.format("%s: %s", getType().getDescription(), params.getState() == 1 ? "闭合" : "断开");
    }
}