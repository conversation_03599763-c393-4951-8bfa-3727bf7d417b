package com.yunqu.park.iot.model.dto.command;

import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.Max;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 设置急转弯报警请求
 * 
 * <AUTHOR>
 * @since 2025-01-06
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class SetHarshTurningAlarmRequest extends BaseCommandReq {
    
    /**
     * 是否启用急转弯报警
     * true: 启用, false: 禁用
     */
    @NotNull(message = "急转弯报警开关不能为空")
    private Boolean enabled;
    
    /**
     * 急转弯阈值 (度/秒: 1-180)
     */
    @Min(value = 1, message = "急转弯阈值不能小于1")
    @Max(value = 180, message = "急转弯阈值不能大于180")
    private Integer threshold;
}