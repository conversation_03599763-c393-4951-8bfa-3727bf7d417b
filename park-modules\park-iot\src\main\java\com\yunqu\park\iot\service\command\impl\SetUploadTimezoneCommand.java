package com.yunqu.park.iot.service.command.impl;

import com.yunqu.park.iot.model.command.CommandType;
import com.yunqu.park.iot.model.command.dto.SetUploadTimezoneParams;
import com.yunqu.park.iot.service.command.ICommand;
import lombok.RequiredArgsConstructor;

@RequiredArgsConstructor
public class SetUploadTimezoneCommand implements ICommand {

    private final SetUploadTimezoneParams params;
    private static final String TEMPLATE = "UTC,%d#";

    @Override
    public CommandType getType() {
        return CommandType.SET_UPLOAD_TIMEZONE;
    }

    @Override
    public String build() {
        return String.format(TEMPLATE, params.getOffsetHours());
    }

    @Override
    public String getDescription() {
        return String.format("%s: UTC+%d", getType().getDescription(), params.getOffsetHours());
    }
}