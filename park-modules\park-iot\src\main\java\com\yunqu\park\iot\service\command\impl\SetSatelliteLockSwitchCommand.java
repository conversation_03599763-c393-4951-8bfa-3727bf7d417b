package com.yunqu.park.iot.service.command.impl;

import com.yunqu.park.iot.model.command.CommandType;
import com.yunqu.park.iot.model.command.dto.SetSatelliteLockSwitchParams;
import com.yunqu.park.iot.service.command.ICommand;
import lombok.RequiredArgsConstructor;

@RequiredArgsConstructor
public class SetSatelliteLockSwitchCommand implements ICommand {

    private final SetSatelliteLockSwitchParams params;
    private static final String TEMPLATE = "LOCKIP,%s#";

    @Override
    public CommandType getType() {
        return CommandType.SET_SATELLITE_LOCK_SWITCH;
    }

    @Override
    public String build() {
        return String.format(TEMPLATE, params.getState());
    }

    @Override
    public String getDescription() {
        return String.format("%s: %s", getType().getDescription(), params.getState());
    }
}