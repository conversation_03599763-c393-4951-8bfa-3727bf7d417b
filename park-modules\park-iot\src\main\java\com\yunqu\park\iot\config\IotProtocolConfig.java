package com.yunqu.park.iot.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * IoT协议配置
 * 用于配置协议解析的各种参数
 *
 * <AUTHOR>
 */
@Data
@Component
@ConfigurationProperties(prefix = "iot.protocol")
public class IotProtocolConfig {

    /**
     * 是否启用严格的CRC校验
     * 开发环境可以设置为false，生产环境建议设置为true
     */
    private boolean strictCrcValidation = false;

    /**
     * 是否启用协议调试日志
     */
    private boolean debugEnabled = true;

    /**
     * 最大数据包大小（字节）
     */
    private int maxPacketSize = 1024;

    /**
     * 连接超时时间（秒）
     */
    private int connectionTimeout = 300;

    /**
     * 心跳间隔（秒）
     */
    private int heartbeatInterval = 60;

    /**
     * 是否自动注册新设备
     */
    private boolean autoRegisterDevice = true;

    /**
     * 支持的协议版本
     */
    private String[] supportedVersions = {"GT06", "GT02", "GT09"};

    /**
     * CRC校验配置
     */
    private CrcConfig crc = new CrcConfig();

    @Data
    public static class CrcConfig {
        /**
         * 是否启用CRC校验
         */
        private boolean enabled = true;

        /**
         * 是否使用宽松模式（尝试多种字节序和数据范围）
         */
        private boolean lenientMode = true;

        /**
         * CRC校验失败时是否继续处理
         */
        private boolean continueOnFailure = true;

        /**
         * 是否记录CRC校验详细日志
         */
        private boolean verboseLogging = true;
    }
}
