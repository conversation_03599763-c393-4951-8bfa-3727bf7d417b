package com.yunqu.park.iot.mapper;

import com.yunqu.park.common.mybatis.core.mapper.BaseMapperPlus;
import com.yunqu.park.iot.domain.IotLocationData;
import com.yunqu.park.iot.domain.vo.IotLocationDataVo;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

/**
 * 设备定位数据Mapper接口
 *
 * <AUTHOR>
 */
public interface IotLocationDataMapper extends BaseMapperPlus<IotLocationData, IotLocationDataVo> {

    /**
     * 根据IMEI获取最新定位数据
     * @param imei 设备IMEI号
     * @return 最新定位数据
     */
    IotLocationDataVo selectLatestByImei(@Param("imei") String imei);

    /**
     * 根据IMEI和时间范围查询轨迹数据
     * @param imei 设备IMEI号
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 轨迹数据列表
     */
    List<IotLocationDataVo> selectTrackData(@Param("imei") String imei,
                                           @Param("startTime") Date startTime,
                                           @Param("endTime") Date endTime);

    /**
     * 根据IMEI和时间范围查询回放数据(按间隔采样)
     * @param imei 设备IMEI号
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param interval 间隔秒数
     * @return 回放数据列表
     */
    List<IotLocationDataVo> selectPlaybackData(@Param("imei") String imei,
                                              @Param("startTime") Date startTime,
                                              @Param("endTime") Date endTime,
                                              @Param("interval") Integer interval);

    /**
     * 批量插入定位数据
     * @param locationDataList 定位数据列表
     * @return 插入数量
     */
    int batchInsert(@Param("list") List<IotLocationData> locationDataList);
}
