package com.yunqu.park.iot.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.yunqu.park.common.core.domain.R;
import com.yunqu.park.common.web.core.BaseController;
import com.yunqu.park.iot.config.IotLogConfig;
import com.yunqu.park.iot.monitor.IotMetricsCollector;
import com.yunqu.park.iot.netty.manager.DeviceConnectionManager;
import com.yunqu.park.iot.utils.IotLogUtils;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.actuate.health.Health;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;

/**
 * IoT监控控制器
 * 提供设备连接状态和日志统计信息
 *
 * <AUTHOR>
 */
@RequiredArgsConstructor
@RestController
@RequestMapping("/iot/monitor")
public class IotMonitorController extends BaseController {

    private final DeviceConnectionManager connectionManager;
    private final IotLogConfig iotLogConfig;

    // 阶段4增强：添加指标收集器
    @Autowired(required = false)
    private IotMetricsCollector metricsCollector;

    /**
     * 获取设备连接统计信息
     */
    @SaCheckPermission("iot:monitor:connection")
    @GetMapping("/connections")
    public R<Object> getConnectionStatistics() {
        Map<String, Object> stats = new HashMap<>();
        stats.put("onlineDeviceCount", connectionManager.getOnlineDeviceCount());
        stats.put("onlineDeviceImeis", connectionManager.getOnlineDeviceImeis());
        stats.put("timestamp", System.currentTimeMillis());

        return R.ok(stats);
    }

    /**
     * 获取IoT系统统计信息
     */
    @SaCheckPermission("iot:monitor:statistics")
    @GetMapping("/statistics")
    public R<String> getSystemStatistics() {
        String statistics = IotLogUtils.getStatistics();
        return R.ok(statistics);
    }

    /**
     * 重置统计计数器
     */
    @SaCheckPermission("iot:monitor:reset")
    @PostMapping("/statistics/reset")
    public R<Void> resetStatistics() {
        IotLogUtils.resetStatistics();
        return R.ok();
    }

    /**
     * 输出当前统计信息到日志
     */
    @SaCheckPermission("iot:monitor:log")
    @PostMapping("/statistics/log")
    public R<Void> logStatistics() {
        IotLogUtils.logStatistics();
        return R.ok();
    }

    /**
     * 检查设备是否在线
     */
    @SaCheckPermission("iot:monitor:device")
    @GetMapping("/device/{imei}/online")
    public R<Boolean> isDeviceOnline(@PathVariable String imei) {
        boolean online = connectionManager.isDeviceOnline(imei);
        return R.ok(online);
    }

    /**
     * 获取系统健康状态
     */
    @SaCheckPermission("iot:monitor:health")
    @GetMapping("/getSystemHealth")
    public R<Object> getSystemHealth() {
        Map<String, Object> health = new HashMap<>();

        // 连接状态
        int onlineCount = connectionManager.getOnlineDeviceCount();
        health.put("onlineDevices", onlineCount);
        health.put("connectionStatus", onlineCount > 0 ? "HEALTHY" : "NO_CONNECTIONS");

        // 系统状态
        health.put("systemStatus", "RUNNING");
        health.put("timestamp", System.currentTimeMillis());

        // 统计信息
        health.put("statistics", IotLogUtils.getStatistics());

        return R.ok(health);
    }

    /**
     * 获取设备详细信息
     */
    @SaCheckPermission("iot:monitor:device")
    @GetMapping("/device/{imei}/details")
    public R<Object> getDeviceDetails(@PathVariable String imei) {
        Map<String, Object> details = new HashMap<>();

        boolean online = connectionManager.isDeviceOnline(imei);
        details.put("imei", imei);
        details.put("online", online);
        details.put("timestamp", System.currentTimeMillis());

        if (online) {
            details.put("status", "CONNECTED");
        } else {
            details.put("status", "DISCONNECTED");
        }

        return R.ok(details);
    }

    /**
     * 获取日志配置状态
     */
    @SaCheckPermission("iot:monitor:log")
    @GetMapping("/log/config")
    public R<Object> getLogConfigStatus() {
        IotLogConfig.LogConfigStatus status = iotLogConfig.getLogConfigStatus();
        return R.ok(status);
    }

    /**
     * 阶段4增强：获取系统健康状态
     */
    @SaCheckPermission("iot:monitor:health")
    @GetMapping("/getHealth")
    public R<Object> getHealth() {
        try {
            if (metricsCollector != null) {
                Health health = metricsCollector.health();

                Map<String, Object> result = new HashMap<>();
                result.put("status", health.getStatus().getCode());
                result.put("details", health.getDetails());
                result.put("timestamp", System.currentTimeMillis());

                return R.ok(result);
            } else {
                Map<String, Object> result = new HashMap<>();
                result.put("status", "UP");
                result.put("message", "基础健康检查通过");
                result.put("timestamp", System.currentTimeMillis());

                return R.ok(result);
            }

        } catch (Exception e) {
            Map<String, Object> result = new HashMap<>();
            result.put("status", "DOWN");
            result.put("error", e.getMessage());
            result.put("timestamp", System.currentTimeMillis());

            return R.fail("健康检查失败: " + e.getMessage(), result);
        }
    }

    /**
     * 阶段4增强：获取性能指标
     */
    @SaCheckPermission("iot:monitor:metrics")
    @GetMapping("/metrics")
    public R<Object> getMetrics() {
        try {
            if (metricsCollector != null) {
                Map<String, Object> metrics = metricsCollector.getAllMetrics();
                metrics.put("timestamp", System.currentTimeMillis());
                return R.ok(metrics);
            } else {
                Map<String, Object> basicMetrics = new HashMap<>();
                basicMetrics.put("active_connections", connectionManager.getOnlineDeviceCount());
                basicMetrics.put("connection_statistics", connectionManager.getConnectionStatistics());
                basicMetrics.put("timestamp", System.currentTimeMillis());
                return R.ok(basicMetrics);
            }

        } catch (Exception e) {
            return R.fail("获取性能指标失败: " + e.getMessage());
        }
    }

    /**
     * 阶段4增强：重置性能统计数据
     */
    @SaCheckPermission("iot:monitor:metrics:reset")
    @PostMapping("/metrics/reset")
    public R<Object> resetMetricsStatistics() {
        try {
            if (metricsCollector != null) {
                metricsCollector.resetStatistics();
            }

            Map<String, Object> result = new HashMap<>();
            result.put("success", true);
            result.put("message", "统计数据已重置");
            result.put("timestamp", System.currentTimeMillis());

            return R.ok(result);

        } catch (Exception e) {
            return R.fail("重置统计数据失败: " + e.getMessage());
        }
    }

    /**
     * 阶段4增强：获取实时监控数据
     */
    @SaCheckPermission("iot:monitor:realtime")
    @GetMapping("/realtime")
    public R<Object> getRealtimeData() {
        try {
            Map<String, Object> data = new HashMap<>();

            // 连接数据
            data.put("active_connections", connectionManager.getOnlineDeviceCount());
            data.put("connection_statistics", connectionManager.getConnectionStatistics());

            // 健康状态和指标
            if (metricsCollector != null) {
                Health health = metricsCollector.health();
                data.put("health_status", health.getStatus().getCode());
                data.put("statistics_summary", metricsCollector.getStatisticsSummary());
            } else {
                data.put("health_status", "UP");
                data.put("statistics_summary", "基础监控模式");
            }

            data.put("timestamp", System.currentTimeMillis());

            return R.ok(data);

        } catch (Exception e) {
            return R.fail("获取实时数据失败: " + e.getMessage());
        }
    }
}
