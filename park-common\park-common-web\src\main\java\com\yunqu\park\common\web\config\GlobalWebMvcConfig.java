package com.yunqu.park.common.web.config;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Configuration;
import org.springframework.format.FormatterRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

/**
 * 全局Web MVC配置
 * 注册全局类型转换器和格式化器
 *
 * <AUTHOR>
 */
@Slf4j
@Configuration
@RequiredArgsConstructor
public class GlobalWebMvcConfig implements WebMvcConfigurer {

    private final GlobalDateConverterConfig.StringToDateConverter stringToDateConverter;
    private final GlobalDateConverterConfig.StringToSqlDateConverter stringToSqlDateConverter;
    private final GlobalDateConverterConfig.StringToTimestampConverter stringToTimestampConverter;

    /**
     * 添加全局类型转换器
     */
    @Override
    public void addFormatters(FormatterRegistry registry) {
        // 注册String到各种Date类型的转换器
        registry.addConverter(stringToDateConverter);
        registry.addConverter(stringToSqlDateConverter);
        registry.addConverter(stringToTimestampConverter);
        
        log.info("Global date converters registered successfully:");
        log.info("  - String to java.util.Date");
        log.info("  - String to java.sql.Date");
        log.info("  - String to java.sql.Timestamp");
    }
}
