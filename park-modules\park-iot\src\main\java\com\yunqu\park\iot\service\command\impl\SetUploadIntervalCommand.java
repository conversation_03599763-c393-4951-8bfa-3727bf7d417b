package com.yunqu.park.iot.service.command.impl;

import com.yunqu.park.iot.model.command.CommandType;
import com.yunqu.park.iot.model.command.dto.SetUploadIntervalParams;
import com.yunqu.park.iot.service.command.ICommand;
import lombok.RequiredArgsConstructor;

@RequiredArgsConstructor
public class SetUploadIntervalCommand implements ICommand {

    private final SetUploadIntervalParams params;
    private static final String TEMPLATE = "TIMER,%d#";

    @Override
    public CommandType getType() {
        return CommandType.SET_UPLOAD_INTERVAL;
    }

    @Override
    public String build() {
        return String.format(TEMPLATE, params.getInterval());
    }

    @Override
    public String getDescription() {
        return String.format("%s: %d秒", getType().getDescription(), params.getInterval());
    }
}